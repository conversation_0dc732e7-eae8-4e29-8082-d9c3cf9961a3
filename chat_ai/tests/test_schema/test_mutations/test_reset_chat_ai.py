from unittest.mock import MagicMock

from django.utils.translation import gettext_lazy as _

from chat_ai.models import Conversation
from common.tests.factories import (
    UserFactory,
    BaseTestMixin,
    WorkspaceFactory,
    LayerFactory,
    ConversationFactory,
    MessageFactory,
)


class TestResetChatAI(BaseTestMixin):
    def setUp(self):
        super().setUp()

        self.workspace = WorkspaceFactory(
            name="Test Workspace", organization=self.organization
        )
        self.layer = LayerFactory()
        self.workspace.layers.add(self.layer)

        # Create the mutation string
        self.mutation = """
        mutation ResetChat($input: ResetNewChatInputType!) {
          resetChatAi(dataInput: $input) {
            success
          }
        }
        """

        # Create the variables for the mutation
        self.variables = {
            "input": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "layersIds": [self.layer.id],
            }
        }


class TestValidation(TestResetChatAI):
    def test_query_authorization(self):
        """Test that unauthorized users cannot execute the mutation."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an unauthorized error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_workspace_validation(self):
        """Test that invalid workspace IDs are rejected."""
        # Use an invalid workspace ID
        self.variables["input"]["workspaceId"] = 9999

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query fails with a workspace validation error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"workspaceId": _("Invalid workspace_id") % {}},
        )

    def test_layers_validation(self):
        """Test that invalid layer IDs are rejected."""
        # Use an invalid layer ID
        self.variables["input"]["layersIds"] = [9999]

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query fails with a layers validation error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"layersIds": _("No layers found with the provided IDs") % {}},
        )


class TestPermission(TestResetChatAI):
    def test_organization_permission(self):
        """Test that users without organization permissions cannot execute the mutation."""
        # Create a user without organization permissions
        user_without_perms = UserFactory(is_superuser=False)

        # Create a request context with the user without permissions
        request = MagicMock()
        request.user = user_without_perms
        request.organization = self.organization

        response = self.client.execute(
            self.mutation, variables=self.variables, context=request
        )

        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertIn("Unauthorized", response["errors"][0]["message"])


class TestExecution(TestResetChatAI):
    def test_successful_reset_with_existing_empty_conversation(self):
        """Test successful reset with an existing empty conversation."""
        # Create an empty conversation with the layer
        conversation = ConversationFactory(user=self.user)
        conversation.layers.add(self.layer)

        # Count conversations before execution
        conversation_count_before = Conversation.objects.count()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["resetChatAi"]
        self.assertTrue(data["success"])

        # Verify that no new conversation was created
        conversation_count_after = Conversation.objects.count()
        self.assertEqual(conversation_count_before, conversation_count_after)

        # Verify that the layer was added to the existing conversation
        conversation.refresh_from_db()
        self.assertIn(self.layer, conversation.layers.all())

    def test_successful_reset_without_existing_empty_conversation(self):
        """Test successful reset without an existing empty conversation."""
        # Create a conversation with messages
        conversation = ConversationFactory(user=self.user)
        conversation.layers.add(self.layer)
        MessageFactory(conversation=conversation)

        # Count conversations before execution
        conversation_count_before = Conversation.objects.count()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["resetChatAi"]
        self.assertTrue(data["success"])

        # Verify that a new conversation was created
        conversation_count_after = Conversation.objects.count()
        self.assertEqual(conversation_count_before + 1, conversation_count_after)

        # Verify that the new conversation has the layer
        new_conversation = Conversation.objects.latest("created")
        self.assertIn(self.layer, new_conversation.layers.all())

        # Verify that the new conversation has no messages
        self.assertEqual(new_conversation.messages.count(), 0)
