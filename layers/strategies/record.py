import logging
from threading import Thread

from gabbro.graphene import BadRequest

from common.interfaces import Strategy
from common.utils import geometry_collection_from_string, fetch_queryset_in_chunks
from layers.mixins import LayerMixin
from layers.models import Layer, Record
from layers.serializers import RecordSerializer
from organizations.models import Organization
from users.models import User

logger = logging.getLogger("layers")


class UpdateRecordsSummaryFieldsStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict[str, Layer],
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        layer = context["layer"]
        map_data_columns = data_input["map_data_columns"]
        layer.workspace.save(update_fields=["modified"])
        thread = Thread(target=self.run_in_background, args=(layer, map_data_columns))
        thread.start()

    def run_in_background(self, layer, map_data_columns):
        all_records = layer.records.only("id", "source_properties")
        self.debug("handle", f"Layer {layer.id}")
        layer.data["summary_fields"] = map_data_columns
        layer.save(update_fields=["data", "modified"])
        for records_batch in fetch_queryset_in_chunks(all_records, 5000):
            for record in records_batch:
                record.map_data = {
                    column: record.source_properties.get(column)
                    or record.data.get(column)
                    for column in map_data_columns
                }
            Record.objects.bulk_update(records_batch, ["map_data"])
            self.debug(
                "handle", f"Layer {layer.id} records updated {len(records_batch)}"
            )
        self.debug("handle", f"Layer {layer.id} records summary fields updated")


class CreateRecordsStrategy(Strategy, LayerMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict[str, Layer],
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        layer = context["layer"]
        form_data = data_input["form_data"]
        summary_fields = layer.data.get("summary_fields", [])
        serializer_data = {
            "layer": layer.id,
            "data": form_data,
            "map_data": {column: form_data.get(column) for column in summary_fields},
            "geometry": geometry_collection_from_string(data_input["geometry"]),
        }
        serializer = RecordSerializer(data=serializer_data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        record = serializer.save()
        self.update_layer_boundaries(layer=layer)
        layer.workspace.increase_records_count()
        layer.update_records_last_modified()
        return record


class UpdateRecordStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict[str, Record],
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        record = context["record"]
        record.data = {**record.data, **data_input["form_data"]}
        record.map_data = {key: record.data.get(key) for key in record.map_data.keys()}
        record.save(update_fields=["data", "map_data", "modified"])
        record.layer.update_records_last_modified()
        record.layer.workspace.save(update_fields=["modified"])
        return record
