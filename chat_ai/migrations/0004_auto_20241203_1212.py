# Generated by Django 3.2.25 on 2024-12-03 12:12

import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0008_alter_layer_status"),
        ("chat_ai", "0003_rename_chatgptconversation_conversation"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="conversation",
            options={
                "verbose_name": "Conversation",
                "verbose_name_plural": "Conversations",
            },
        ),
        migrations.AlterField(
            model_name="conversation",
            name="layer",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="conversation",
                to="layers.layer",
                verbose_name="layer",
            ),
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "message",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        help_text="contains user input, GPT output, and SQL Query",
                        verbose_name="message",
                    ),
                ),
                (
                    "conversation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="chat_ai.conversation",
                        verbose_name="conversation",
                    ),
                ),
            ],
            options={"verbose_name": "Message", "verbose_name_plural": "Messages"},
        ),
    ]
