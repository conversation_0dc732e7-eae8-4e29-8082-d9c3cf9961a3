from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timesince
from django.utils.translation import gettext_lazy as _

from common.tests.factories import BaseTestMixin, WorkspaceFactory, UserFactory
from workspaces.models import Workspace

User = get_user_model()


class TestWorkspaces(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.query = """
        query MyQuery($orgId: Int!) {
          workspaces(orgId: $orgId) {
            count
            data {
              id
              thumbnail
              since
              name
              lastVisited
              description
              created
            }
          }
        }
        """
        self.variables = {"orgId": self.organization.id}
        self.workspaces = WorkspaceFactory.create_batch(10, owner=self.user)

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaces"]
        self.assertEqual(data["count"], 1)
        data = data["data"][0]
        workspace = Workspace.objects.filter(id=data["id"]).first()
        self.assertEqual(
            data["name"],
            workspace.name,
        )
        self.assertEqual(
            data["thumbnail"],
            workspace.thumbnail,
        )
        self.assertEqual(
            data["since"],
            timesince.timesince(workspace.created, depth=1),
        )
        self.assertEqual(
            data["lastVisited"],
            workspace.last_visited,
        )
        self.assertEqual(
            data["description"],
            workspace.description,
        )

    def test_query_with_superuser(self):
        user = UserFactory(is_superuser=False)
        WorkspaceFactory(owner=user)
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaces"]
        self.assertEqual(
            data["count"],
            Workspace.objects.filter(organization=self.organization).count(),
        )

    def test_query_while_user_has_no_workspace(self):
        Workspace.objects.all().delete()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaces"]
        self.assertEqual(data["count"], 1)
        data = data["data"][0]
        self.assertEqual(data["name"], "مساحة عمل افتراضية")
        self.assertEqual(
            data["thumbnail"], f"{settings.STATIC_URL}workspaces/default_thumbnail.png"
        )
