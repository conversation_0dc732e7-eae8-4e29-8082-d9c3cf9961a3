import logging
from threading import Thread
from typing import <PERSON><PERSON>
from uuid import uuid4

from django.db import transaction
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from chat_ai.models import Conversation
from common.interfaces import Strategy
from common.utils import get_random_color, fetch_queryset_in_chunks
from layers.mixins import LayerMixin
from layers.models import Layer, Record, SLDMapTypes, SLD
from layers.models.typed_dict import HeatMap
from layers.scripts import PublishLayerToGeoserver
from layers.serializers import (
    LayerSerializer,
    SLDSerializer,
)
from organizations.models import Organization
from users.models import User
from workspaces.models import Dataset, Workspace

logger = logging.getLogger("layers")


class CreateLayerFromDatasetStrategy(Strategy, LayerMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        workspace = context["workspace"]
        layer, sld = self.create_layer(dataset=context["dataset"], workspace=workspace)
        workspace.increase_layers_count()

        publisher = PublishLayerToGeoserver(sld=sld, layer=layer)
        publisher.publish()

        Thread(target=self.run_in_background, args=(layer, workspace)).start()
        return layer

    def run_in_background(self, layer: Layer, workspace: Workspace):
        geometry_columns = self.get_geometry_columns(layer=layer)
        self.debug("run_in_background", f"geometry_columns: {geometry_columns}")
        self.create_geometry_records_from_dataset_file(
            layer=layer,
            geometry_columns=geometry_columns,
            summary_fields=layer.data.get("summary_fields", []) or [],
        )
        self.update_layer_boundaries(layer=layer)
        workspace.increase_records_count(layer.records.count())

    @staticmethod
    def create_layer(
        dataset: Dataset, workspace: Workspace, **kwargs
    ) -> Tuple[Layer, SLD]:
        layer = Layer.objects.create(
            dataset=dataset,
            workspace=workspace,
            title=dataset.title,
            key=f"{workspace.pk}_{str(uuid4()).split('-')[0]}",
            data={"summary_fields": dataset.meta_data.get("summary_fields")},
            json_schema=dataset.meta_data.get("json_schema"),
            web_ui_json_schema=dataset.meta_data.get("web_ui_json_schema"),
            location_field_mapping=dataset.meta_data.get("location_field_mapping"),
        )
        sld = SLD.objects.create(
            title=layer.key, feature_style={"color": get_random_color()}
        )
        layer.slds.add(sld)
        return layer, sld


class UpdateLayerStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict[str, Layer],
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        serializer = LayerSerializer(
            data=data_input, instance=context["layer"], partial=True
        )
        serializer.is_valid()
        layer = serializer.save()
        layer.workspace.save(update_fields=["modified"])
        return layer


class DeleteLayerStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        layer = context["layer"]
        workspace = layer.workspace
        Conversation.objects.filter(layers__id=layer.id).delete()
        workspace.decrease_layers_count()
        workspace.decrease_records_count(layer.records.count())
        layer.delete()


class DuplicateLayerStrategy(Strategy, LayerMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        layer = context["layer"]
        workspace = layer.workspace
        new_layer = Layer.objects.create(
            dataset=layer.dataset,
            workspace=workspace,
            title=layer.title,
            key=f"{workspace.pk}_{str(uuid4()).split('-')[0]}",
            data=layer.data,
            json_schema=layer.json_schema,
        )
        sld = SLD.objects.create(
            title=new_layer.key, feature_style={"color": get_random_color()}
        )
        new_layer.slds.add(sld)
        workspace.increase_layers_count()

        publisher = PublishLayerToGeoserver(layer=new_layer, sld=sld)
        publisher.publish()

        Thread(
            target=self.run_in_background, args=(layer, new_layer, workspace)
        ).start()
        return new_layer

    def run_in_background(
        self, layer_source: Layer, layer_target: Layer, workspace: Workspace
    ):
        self.create_geometry_records_from_layer(
            layer_source=layer_source, layer_target=layer_target
        )
        layer_target.boundaries = layer_source.boundaries
        layer_target.save(update_fields=["boundaries", "modified"])
        workspace.increase_layers_count()
        workspace.increase_records_count(layer_target.records.count())


class LayerHeatMapStrategy(Strategy, LayerMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ) -> SLD:
        layer: Layer = context["layer"]
        heatmap_attributes = context["heatmap_attributes"]
        weight_field = heatmap_attributes["weight_field"]
        density_range = heatmap_attributes.get("density_range")

        # Query the records linked to the layer
        records_qs = layer.records.only("id", "data")
        if density_range:
            records_qs = self._filter_by_density_range(
                records_qs, weight_field, density_range
            )

        # If no records match the criteria, raise a BadRequest error.
        if not records_qs.exists():
            raise BadRequest(
                reason={"weight_field": _("No data found in the specified range.") % {}}
            )

        # Save the processed heatmap attributes to the layer and update its modification time.
        heatmap_attributes["weight_field"] = "weight"
        heatmap_attributes["data_weight_field"] = weight_field
        heatmap_sld = self._update_layer_sld(layer, heatmap_attributes)

        publisher = PublishLayerToGeoserver(layer=layer, sld=heatmap_sld)
        publisher.unpublish_style()
        publisher.publish_style()

        # Start a background thread to update records' weight values asynchronously.
        Thread(
            target=self.run_in_background,
            args=(layer, records_qs, weight_field),
            daemon=True,
        ).start()
        return heatmap_sld

    @staticmethod
    def _filter_by_density_range(queryset, weight_field: str, density_range: list):
        from_val, to_val = density_range
        return queryset.filter(
            Q(**{f"data__{weight_field}__gte": from_val})
            & Q(**{f"data__{weight_field}__lte": to_val})
        )

    @staticmethod
    def _update_layer_sld(layer: Layer, heatmap_attributes: HeatMap):
        heatmap_sld = layer.slds.filter(sld_type=SLDMapTypes.HEATMAP).last()
        if not heatmap_sld:
            heatmap_sld = SLD.objects.create(
                title=f"{layer.key}_heat_map",
                sld_type=SLDMapTypes.HEATMAP,
                feature_style=heatmap_attributes,
            )
            layer.slds.add(heatmap_sld)
        else:
            heatmap_sld.feature_style = heatmap_attributes
            heatmap_sld.save(update_fields=["feature_style", "modified"])
        return heatmap_sld

    def run_in_background(self, layer: Layer, records, weight_field: str):
        for records_batch in fetch_queryset_in_chunks(records, 5000):
            for record in records_batch:
                record.weight = record.data.get(weight_field)
            Record.objects.bulk_update(records_batch, ["weight", "modified"])
            self.debug(
                "run_in_background",
                f"Updated {len(records_batch)} records for layer {layer.id}",
            )

        self.debug(
            "run_in_background", f"Completed weight field update for layer {layer.id}"
        )


class UpdateLayerSLDStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict[str, Layer],
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        layer: Layer = context["layer"]
        sld = layer.slds.filter(sld_type=SLDMapTypes.POINTS).last()
        if sld:
            serializer = SLDSerializer(
                instance=sld, data={"feature_style": data_input}, partial=bool(sld)
            )
            serializer.is_valid()
            sld = serializer.save()
        else:
            serializer = SLDSerializer(
                data={"title": layer.key, "feature_style": data_input}
            )
            serializer.is_valid()
            sld = serializer.save()
            layer.slds.add(sld)

        publisher = PublishLayerToGeoserver(layer=layer, sld=sld)
        publisher.update_style()
        return sld


class UpdateLayerFiltersStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict[str, Layer],
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        layer: Layer = context["layer"]
        layer.filters = data_input["filters"]
        layer.save(update_fields=["filters", "modified"])
        layer.workspace.save(update_fields=["modified"])
        return layer
