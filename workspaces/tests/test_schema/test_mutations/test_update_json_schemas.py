import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequestChoices, RequestTypeChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestUpdateJSONSchemas(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.geojson"),
            meta_data={},
        )
        # Create a workspace request in IN_PROGRESS state
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            request_type=RequestTypeChoices.UPLOAD_FILE,
            layer_data={"title": "Test Dataset", "color": "#e3cec7"},
        )
        self.mutation = """
        mutation MyMutation($datasetRequestId: Int!, $formData: JSONString!, $jsonSchema: JSONString!, $webUiJsonSchema: JSONString!, $orgId: Int!) {
          updateJsonSchemas(
            dataInput: {datasetRequestId: $datasetRequestId, formData: $formData, jsonSchema: $jsonSchema, webUiJsonSchema: $webUiJsonSchema, orgId: $orgId}
          ) {
            datasetRequest {
              id
              status
              requestType
              layerData
              currentStep
              createdBy {
                id
                email
              }
            }
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "formData": '{"test":"test"}',
            "jsonSchema": '{"type":"object","properties":{"test":{"type":"string"}}}',
            "webUiJsonSchema": '{"ui:order":["test"]}',
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot update JSON schemas."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_dataset_request_id(self):
        """Test that an invalid dataset request ID is handled properly."""
        # Use a non-existent dataset request ID
        invalid_variables = self.variables.copy()
        invalid_variables["datasetRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a dataset request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceRequestId": "Dataset with 99999 not found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_cancelled_dataset_request(self):
        """Test that a cancelled dataset request is handled properly."""
        # Cancel the request first
        self.dataset_request.status = WorkspaceRequestChoices.CANCELLED
        self.dataset_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query fails with a dataset request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {self.dataset_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_invalid_json_schema(self):
        """Test that an invalid JSON schema is handled properly."""
        # Use an invalid JSON schema
        invalid_variables = self.variables.copy()
        invalid_variables["jsonSchema"] = '{"type":"invalid"}'

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a JSON schema error
        self.assertIn("errors", response)
        self.assertDictEqual(
            {
                "http": {
                    "reason": {
                        "jsonSchema": "'invalid' is not valid under any of the given schemas"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
            response["errors"][0]["extensions"],
        )

    def test_other_user_request_regular_user(self):
        """Test that a regular user cannot update another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            request_type=RequestTypeChoices.UPLOAD_FILE,
            layer_data={"title": "Other User Dataset", "color": "#e3cec7"},
        )

        # Try to update the other user's request
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {other_user_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_request_superuser(self):
        """Test that a superuser can update another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            request_type=RequestTypeChoices.UPLOAD_FILE,
            layer_data={"title": "Other User Dataset", "color": "#e3cec7"},
        )

        # Try to update the other user's request as a superuser
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)

        # Verify the JSON schemas were added to the layer data
        other_user_request.refresh_from_db()
        self.assertIn("json_schema", other_user_request.layer_data)
        self.assertIn("web_ui_json_schema", other_user_request.layer_data)

    def test_successful_update(self):
        """Test successful update of JSON schemas."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["updateJsonSchemas"]["datasetRequest"]
        self.assertEqual(data["id"], self.dataset_request.id)

        # Verify the JSON schemas were added to the layer data
        self.dataset_request.refresh_from_db()
        self.assertIn("json_schema", self.dataset_request.layer_data)
        self.assertIn("web_ui_json_schema", self.dataset_request.layer_data)

        # Verify the current step is updated
        self.assertEqual(data["currentStep"], 5)  # Step 5 is after JSON schema is added
