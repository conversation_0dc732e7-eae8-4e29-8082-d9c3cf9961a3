# Generated by Django 3.2.25 on 2024-11-02 22:24

import re

import django.contrib.gis.db.models.fields
import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("workspaces", "0002_alter_workspace_thumbnail"),
    ]

    operations = [
        migrations.CreateModel(
            name="Layer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "key",
                    models.SlugField(
                        max_length=200, unique=True, verbose_name="Unique Form Key"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Layer Title")),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Layer Description"
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#B22222",
                        max_length=7,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Hex Color Invalid!",
                                regex=re.compile("^#([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$"),
                            )
                        ],
                        verbose_name="Fill Color",
                    ),
                ),
                (
                    "read_only",
                    models.BooleanField(
                        default=False,
                        help_text="Is this layer editable or not?",
                        verbose_name="Read Only",
                    ),
                ),
                (
                    "location_field_mapping",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Location Field Mapping"
                    ),
                ),
                (
                    "json_schema",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Form and UI Schemas",
                        verbose_name="JSON Schema",
                    ),
                ),
                (
                    "web_ui_json_schema",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Layer Web UI JsonSchema"
                    ),
                ),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="layers",
                        to="workspaces.dataset",
                        verbose_name="Dataset",
                    ),
                ),
                (
                    "workspace",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="layers",
                        to="workspaces.workspace",
                        verbose_name="Workspace",
                    ),
                ),
            ],
            options={
                "verbose_name": "Layer",
                "verbose_name_plural": "Layers",
            },
        ),
        migrations.CreateModel(
            name="Record",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "geometry",
                    django.contrib.gis.db.models.fields.GeometryField(
                        blank=True,
                        null=True,
                        srid=4326,
                        verbose_name="Geometry Collection Record",
                    ),
                ),
                (
                    "source_properties",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Source Properties"
                    ),
                ),
                (
                    "map_data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, null=True, verbose_name="Map Data"
                    ),
                ),
                (
                    "data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, null=True, verbose_name="Order Data Dependency"
                    ),
                ),
                (
                    "layer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="layers.layer",
                        verbose_name="Related Layer",
                    ),
                ),
            ],
            options={
                "verbose_name": "Geometry Record",
                "verbose_name_plural": "Geometry Records",
            },
        ),
    ]
