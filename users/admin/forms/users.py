from dal import autocomplete
from django.contrib.auth import get_user_model
from django.forms import ModelForm


class UserForm(ModelForm):
    class Meta:
        model = get_user_model()
        fields = (
            "email",
            "phone",
            "first_name",
            "last_name",
            "is_staff",
            "is_superuser",
            "active_status",
            "groups",
        )
        widgets = {
            "phone": autocomplete.Select2(
                url="phone-autocomplete", attrs={"data-html": True}
            ),
            "email": autocomplete.Select2(
                url="email-autocomplete", attrs={"data-html": True}
            ),
        }
