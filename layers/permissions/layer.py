from common.interfaces import PermissionsInterface
from common.utils import authorize_user, authorize_multiple_objects_for_user
from layers.models import Layer
from organizations.models import Organization
from organizations.perms_constants import (
    CHANGE_WORKSPACE,
    ADD_WORKSPACE,
    DELETE_WORKSPACE,
)


class UpdateLayerPermissions(PermissionsInterface):
    def check_permissions(
        self, user, organization: Organization, context: dict[str, Layer]
    ):
        layer = context["layer"]
        authorize_multiple_objects_for_user(
            models_objs=[organization, layer.workspace],
            perm=CHANGE_WORKSPACE,
            user=user,
        )


class CreateLayerFromDatasetPermissions(PermissionsInterface):
    def check_permissions(self, user, organization: Organization, context):
        authorize_user(model_obj=organization, user=user, permission=ADD_WORKSPACE)


class DeleteLayerPermissions(PermissionsInterface):
    def check_permissions(self, user, organization: Organization, context):
        layer = context["layer"]
        authorize_multiple_objects_for_user(
            models_objs=[organization, layer.workspace],
            perm=DELETE_WORKSPACE,
            user=user,
        )
