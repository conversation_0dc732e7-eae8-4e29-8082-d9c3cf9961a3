from io import BytesIO
from typing import List, Dict

import geopandas as gpd
from fastkml import kml
from shapely.geometry import shape


class KMLHandler:
    """
    A handler class for parsing KML files and converting them to a GeoDataFrame.
    """

    def __init__(self, file_obj: BytesIO):
        """
        Initialize the KMLHandler with a file-like object containing KML data.

        :param file_obj: File-like object for the KML file.
        """
        self.file_obj = file_obj

    def load_kml(self) -> gpd.GeoDataFrame:
        """
        Load the KML file and convert it to a GeoDataFrame.

        :return: GeoDataFrame containing features from the KML file.
        """
        k = kml.KML()
        k.from_string(self.file_obj.read())
        features = []

        for feature in k.features():
            self._extract_placemarks(feature, features)

        return gpd.GeoDataFrame(features, crs="EPSG:4326")

    def _extract_placemarks(self, kml_element, features: List[Dict]):
        """
        Recursively extract placemarks from a KML element and append to the feature list.

        :param kml_element: A KML element (e.g., Document, Folder, Placemark).
        :param features: List to collect extracted feature dictionaries.
        """
        for element in kml_element.features():
            if isinstance(element, kml.Placemark):
                feature_data = self._get_placemark_data(element)
                features.append(feature_data)
            elif hasattr(element, "features"):
                # Recurse into nested Folders or Documents
                self._extract_placemarks(element, features)

    def _get_placemark_data(self, placemark) -> Dict:
        """
        Extract geometry and properties from a KML Placemark.

        :param placemark: A KML Placemark object.
        :return: Dictionary with geometry and properties.
        """
        geom = shape(placemark.geometry)
        properties = self._get_properties_from_extended_data(placemark)
        properties["geometry"] = geom
        return properties

    def _get_properties_from_extended_data(self, placemark) -> Dict:
        """
        Extract properties from a placemark's extended data, if present.

        :param placemark: A KML Placemark object.
        :return: Dictionary of properties extracted from extended data.
        """
        properties = {}
        if placemark.extended_data and placemark.extended_data.elements:
            for item in placemark.extended_data.elements[0].data:
                if isinstance(item, dict):
                    properties.update(item)
                else:
                    properties[item.name] = item.value
        return properties
