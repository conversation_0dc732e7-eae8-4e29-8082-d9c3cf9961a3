import os
from unittest.mock import patch, MagicMock

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test import TransactionTestCase
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.enums import LongLatEnum
from workspaces.models import WorkspaceRequest, WorkspaceRequestChoices

User = get_user_model()


class TestCreateLogFieldMapping(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg"),
            meta_data={},
        )
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        self.file_path = os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        self.mutation = """
        mutation MyMutation($longitudeColumn: String!, $latitudeColumn: String!, $latLonColumnNum: LongLatEnum!, $langLatColumn: String!, $datasetRequestId: Int!, $orgId: Int!, $coordinateType: CoordinateTypesEnum!) {
          createLocationFieldMapping(
            dataInput: {datasetRequestId: $datasetRequestId, orgId: $orgId, coordinateType: $coordinateType, latLonColumnNum: $latLonColumnNum, langLatColumn: $langLatColumn, latitudeColumn: $latitudeColumn, longitudeColumn: $longitudeColumn}
          ) {
            datasetRequest {
              status
              layerData
              currentStep
              id
              dataset {
                file
                metaData
              }
              createdBy {
                email
              }
            }
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "coordinateType": "point",
            "latitudeColumn": "X",
            "latLonColumnNum": "column",
            "longitudeColumn": "Y",
            "langLatColumn": "geom",
            "orgId": self.organization.id,
        }


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestValidation(TestCreateLogFieldMapping):
    def test_query_authorization(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_validate_and_get_data_func(self):
        dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.FINISHED,
            layer_data={},
            organization=self.organization,
        )
        self.variables["datasetRequestId"] = dataset_request.id
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "workspaceRequestId": _("Dataset with %(request_id)s not found")
                % {"request_id": dataset_request.id}
            },
        )


class TestPermission(TestCreateLogFieldMapping):
    def test_check_permissions_func(self):
        user = UserFactory(is_superuser=False)
        dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            layer_data={"data": {"data": "data"}},
            organization=self.organization,
        )
        self.variables["datasetRequestId"] = dataset_request.id
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"workspaceRequestId": f"Dataset with {dataset_request.id} not found"},
        )


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestStrategy(TestCreateLogFieldMapping, TransactionTestCase):
    def test_get_columns_func(self):
        self.variables["lang_lat_column"] = LongLatEnum.two_column.value
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "error": _("columns %(columns)s not included in the dataset")
                % {"columns": ["geom"]}
            },
        )

    def test_get_sample_data_from_dataset_request_func(self):
        self.variables["langLatColumn"] = "geometry"
        dataset_request = WorkspaceRequest.objects.get(id=self.dataset_request.id)
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["createLocationFieldMapping"]
        self.assertEqual(data["datasetRequest"]["status"], "IN_PROGRESS")
        self.assertEqual(
            data["datasetRequest"]["layerData"]["location_field_mapping"][
                "lang_lat_column"
            ],
            "geometry",
        )
        self.assertEqual(
            data["datasetRequest"]["layerData"]["location_field_mapping"][
                "coordinate_type"
            ],
            "point",
        )
        self.assertEqual(
            data["datasetRequest"]["layerData"]["location_field_mapping"][
                "lat_lon_column_num"
            ],
            "column",
        )
        self.assertEqual(data["datasetRequest"]["currentStep"], 4)
        dataset = data["datasetRequest"]["dataset"]
        self.assertDictEqual(
            dataset["metaData"],
            dataset_request.dataset.meta_data,
        )
        self.assertEqual(
            dataset["file"],
            dataset_request.dataset.file,
        )
        created_by = data["datasetRequest"]["createdBy"]
        self.assertEqual(
            created_by["email"],
            dataset_request.created_by.email,
        )

    def test_mutation_with_2_columns_geometry(self):
        dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.csv"),
        )
        dataset_request = WorkspaceRequestFactory(
            dataset=dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        self.variables["latLonColumnNum"] = "two_column"
        self.variables["datasetRequestId"] = dataset_request.id
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

    @patch(
        "workspaces.strategies.request.CreateLocationFieldMappingStrategy.get_sample_data_from_dataset_file"
    )
    def test_invalid_sample_data(self, mock_get_sample_data_from_dataset_request):
        mock_get_sample_data_from_dataset_request.return_value = {"TEST": "TEST"}
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"error": _("Invalid geometry columns ['geom']") % {}},
        )

    def test_with_sample_data(self):
        sample_data = [
            {
                "data": [
                    "39.127902897026",
                    "39.1440719092567",
                    "39.1267282572396",
                    "39.1296885344199",
                    "39.1539532895001",
                ],
                "column": "X",
            },
            {
                "data": [
                    "22.1779350859965",
                    "22.1656821170019",
                    "22.1734079704979",
                    "22.176827142974",
                    "22.184230812",
                ],
                "column": "Y",
            },
            {
                "data": ["1", "70632847", "72462592", "72952076", "74465838"],
                "column": "id",
            },
            {"data": ["3971", "7798", "4331", "5237", "4565"], "column": "parcel_num"},
            {
                "data": ["412?/?", "412?/?", "412?/?", "412?/?", "412?/?"],
                "column": "plan_numbe",
            },
            {"data": ["", "", "", "", ""], "column": "block_numb"},
            {"data": ["????", "????", "????", "????", "????"], "column": "land_use"},
            {"data": ["", "", "", "", ""], "column": "sub_land_u"},
            {"data": ["0", "0", "0", "0", "0"], "column": "commercial"},
            {"data": ["466", "466", "466", "466", "466"], "column": "index_righ"},
            {
                "data": ["FR-3-101", "FR-3-101", "FR-3-101", "FR-3-101", "FR-3-101"],
                "column": "PA_CODE",
            },
        ]
        columns = [
            "X",
            "Y",
            "id",
            "parcel_num",
            "plan_numbe",
            "block_numb",
            "land_use",
            "sub_land_u",
            "commercial",
            "index_righ",
            "PA_CODE",
        ]
        dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.csv"),
        )
        dataset_request = WorkspaceRequestFactory(
            dataset=dataset,
            created_by=self.user,
            organization=self.organization,
        )
        dataset.meta_data["sample_data"] = sample_data
        dataset.meta_data["columns"] = columns
        dataset.save()
        self.variables = {
            "datasetRequestId": dataset_request.id,
            "coordinateType": "point",
            "latitudeColumn": "X",
            "latLonColumnNum": "two_column",
            "longitudeColumn": "Y",
            "langLatColumn": "geometry",
            "orgId": self.organization.id,
        }

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

    def test_with_invalid_sample_data_columns(self):
        sample_data = [
            {
                "data": [
                    "39.127902897026",
                    "39.1440719092567",
                    "39.1267282572396",
                    "39.1296885344199",
                    "39.1539532895001",
                ],
                "column": "X",
            },
            {
                "data": [
                    "22.1779350859965",
                    "22.1656821170019",
                    "22.1734079704979",
                    "22.176827142974",
                    "22.184230812",
                ],
                "column": "Y",
            },
            {
                "data": ["1", "70632847", "72462592", "72952076", "74465838"],
                "column": "id",
            },
            {"data": ["3971", "7798", "4331", "5237", "4565"], "column": "parcel_num"},
            {
                "data": ["412?/?", "412?/?", "412?/?", "412?/?", "412?/?"],
                "column": "plan_numbe",
            },
            {"data": ["", "", "", "", ""], "column": "block_numb"},
            {"data": ["????", "????", "????", "????", "????"], "column": "land_use"},
            {"data": ["", "", "", "", ""], "column": "sub_land_u"},
            {"data": ["0", "0", "0", "0", "0"], "column": "commercial"},
            {"data": ["466", "466", "466", "466", "466"], "column": "index_righ"},
            {
                "data": ["FR-3-101", "FR-3-101", "FR-3-101", "FR-3-101", "FR-3-101"],
                "column": "PA_CODE",
            },
        ]
        columns = [
            "X",
            "Y",
            "id",
            "parcel_num",
            "plan_numbe",
            "block_numb",
            "land_use",
            "sub_land_u",
            "commercial",
            "index_righ",
            "PA_CODE",
        ]
        dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.csv"),
        )
        dataset_request = WorkspaceRequestFactory(
            dataset=dataset,
            created_by=self.user,
            organization=self.organization,
        )
        dataset.meta_data["sample_data"] = sample_data
        dataset.meta_data["codlumns"] = columns
        dataset.save()
        self.variables = {
            "datasetRequestId": dataset_request.id,
            "coordinateType": "point",
            "latitudeColumn": "F",
            "latLonColumnNum": "two_column",
            "longitudeColumn": "Y",
            "langLatColumn": "geometry",
            "orgId": self.organization.id,
        }

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "error": _("columns %(columns)s not included in the dataset")
                % {"columns": ["F", "Y"]}
            },
        )

    @patch("workspaces.strategies.request.LocationFieldMappingSerializer")
    def test_with_invalid_serializer(self, mock_location_field_mapping_serializer):
        self.variables["langLatColumn"] = "geometry"
        mock_instance = MagicMock()
        mock_instance.is_valid.return_value = False
        mock_instance.errors = {"field1": ["This field is required."]}
        mock_location_field_mapping_serializer.return_value = mock_instance
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
