CQLFilterInstructions:
  description: |
    Generate a correct CQL filter from natural language queries to retrieve data from a GeoServer database with JSON fields.
    This YAML contains structured rules, examples, and common patterns for generating CQL filters accurately.
  rules:
    - natural_language_inquiries: List of natural language questions mapped to target CQL filters.
    - json_field_access:
        description: Use `jsonPointer(field, 'json_key')` to access fields within JSON columns.
        example: |
          json<PERSON>ointer(source_properties, 'city') = 'الرياض'
    - basic_structure:
        description: Use the column name directly for non-JSON fields. When accessing a JSON key within a JSON column, use `jsonPointer`.
        example:
          non_json_field: column_name = 'value'
          json_field: jsonPointer(json_column, 'key') = 'value'
    - operators:
        - equal: '='
        - not_equal: '!='
        - greater_than: '>'
        - less_than: '<'
        - like: "LIKE 'value%'"
        - contains: |
            Use `LIKE '%value%'` for partial matching within text fields.
            example: json<PERSON>ointer(source_properties, 'name') LIKE '%له بن الحارث الثانوية (مسائي)%'
    - conditional_logic:
        - and_condition: 'AND'
        - or_condition: 'OR'
  examples:
    - question: How many records are in Riyadh city?
      cql_filter: jsonPointer(source_properties, 'city') = 'الرياض'
    - question: Find records with confidence greater than 80.
      cql_filter: jsonPointer(source_properties, 'confidence') > '80'
    - question: Find published layers.
      cql_filter: status = 'published'
    - question: Show records from a specific workspace with ID 5.
      cql_filter: workspace_id = 5
    - question: Retrieve all records containing "له بن الحارث الثانوية (مسائي)" in the name.
      cql_filter: jsonPointer(source_properties, 'name') LIKE '%له بن الحارث الثانوية (مسائي)%'
  steps:
    1: Parse natural language query to identify field, condition, and value.
    2: If accessing JSON field, use `jsonPointer(column, 'key')`.
    3: For simple fields, use `column_name operator value`.
    4: For partial matches within text fields, use `LIKE '%value%'`.
    5: Combine multiple conditions using `AND` or `OR` as needed.
    6: Return the generated CQL filter without extra characters or explanations.
