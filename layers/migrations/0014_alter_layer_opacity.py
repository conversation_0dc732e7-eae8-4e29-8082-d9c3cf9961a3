# Generated by Django 3.2.25 on 2024-12-31 07:20

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0013_remove_layer_sorting_number"),
    ]

    operations = [
        migrations.AlterField(
            model_name="layer",
            name="opacity",
            field=models.FloatField(
                default=0.5,
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ],
                verbose_name="Layer Opacity",
            ),
        ),
    ]
