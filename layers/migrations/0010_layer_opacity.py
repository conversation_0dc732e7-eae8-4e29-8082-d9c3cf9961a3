# Generated by Django 3.2.25 on 2024-12-23 11:52

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0009_record_buffer_geometry"),
    ]

    operations = [
        migrations.AddField(
            model_name="layer",
            name="opacity",
            field=models.FloatField(
                default=0.2,
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ],
                verbose_name="Layer Opacity",
            ),
        ),
    ]
