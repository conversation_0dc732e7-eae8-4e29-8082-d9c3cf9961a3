from common.interfaces import PermissionsInterface
from common.utils import authorize_user
from organizations.perms_constants import ADD_USER, CHANGE_USER


class AddUserPerms(PermissionsInterface):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=ADD_USER)


class ChangeUserPerms(PermissionsInterface):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=CHANGE_USER)
