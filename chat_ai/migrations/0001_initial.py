# Generated by Django 3.2.25 on 2024-11-13 14:52

import uuid

import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("workspaces", "0004_alter_workspace_organization"),
    ]

    operations = [
        migrations.CreateModel(
            name="ChatGPTConversation",
            fields=[
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="UUID PK",
                    ),
                ),
                (
                    "history",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        default=list,
                        help_text="contains user inputs, GPT outputs, and SQL Queries",
                        verbose_name="conversation data",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conversations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user",
                    ),
                ),
                (
                    "layer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="conversations",
                        to="layers.layer",
                        verbose_name="layer",
                    ),
                ),
            ],
            options={
                "verbose_name": "ChatGPT Conversation",
                "verbose_name_plural": "ChatGPT Conversations",
            },
        ),
    ]
