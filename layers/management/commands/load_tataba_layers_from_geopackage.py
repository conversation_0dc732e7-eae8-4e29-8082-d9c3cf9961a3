import json
import logging
import time
from uuid import uuid4

from django.contrib.gis.geos.error import GEOSException
from django.contrib.gis.geos.prototypes.io import WKBWriter
from django.core.files import File
from django.core.files.storage import default_storage
from django.core.management import BaseCommand, CommandError
from fiona.io import MemoryFile

from common.utils import (
    geometry_collection_from_string,
    convert_fiona_geometry_collection_to_geojson,
    parse_all_properties,
)
from layers.models import Layer, Record
from organizations.models import Organization
from users.models import User
from workspaces.models import Workspace, Dataset

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("--tataba_geopackages_path", type=str, required=True)

    def handle(self, *args, **options):
        start = time.time()
        path = options["tataba_geopackages_path"]
        if not default_storage.exists(path):
            raise CommandError(f"this tataba_geopackages_path not Found {path}")
        loading_layers(path)
        end = time.time()
        self.stdout.write(f"All Layers Loaded in {end-start} seconds")


def loading_layers(path):
    organization, _ = Organization.objects.get_or_create(external_id=1)
    user, _ = User.objects.get_or_create(email="<EMAIL>")
    org_ids = default_storage.listdir(path)[0]
    for org_id in org_ids:
        layer_ids = default_storage.listdir(f"{path}/{org_id}")[0]
        workspace = Workspace.objects.create(
            owner_id=user.id,
            organization_id=organization.id,
            name=f"workspace {org_id}",
        )
        logger.debug(f"Workspace created {workspace.id}")
        for layer_id in layer_ids:
            dataset_file_path = default_storage.listdir(f"{path}/{org_id}/{layer_id}/")[
                1
            ][0]
            dataset_file = default_storage.open(
                f"{path}/{org_id}/{layer_id}/{dataset_file_path}"
            )
            dataset = Dataset.objects.create(file=f"{dataset_file}")
            layer = Layer.objects.create(
                workspace=workspace,
                dataset=dataset,
                key=f"{workspace.pk} - {str(uuid4()).split('-')[0]}",
            )
            logger.debug(f"Layer created {layer.id}")
            features_list = geopackage_to_geojson_generator(file_object=dataset_file)
            create_records_from_features(features_list=features_list, layer=layer)
            logger.debug(f"Finished of loading the file: {dataset_file_path}")
            logger.debug("=" * 70)


def geopackage_to_geojson_generator(file_object: File, batch_size: int = 5000):
    """Generator function to convert GeoPackage layers to GeoJSON-like structures."""
    geo_pkg_bytes = file_object.read()
    batch = []
    with MemoryFile(geo_pkg_bytes) as memory_file:
        for layer_name in memory_file.listlayers():
            with memory_file.open(layer=layer_name) as layer:
                for feature in layer:
                    properties = parse_all_properties(dict(feature["properties"]))
                    geojson_geometry = convert_fiona_geometry_collection_to_geojson(
                        feature["geometry"]
                    )
                    batch.append(
                        {
                            "type": "Feature",
                            "geometry": geojson_geometry,
                            "properties": properties,
                        }
                    )

                    if len(batch) == batch_size:
                        yield batch
                        batch = []
    # yield the last batch if it is not empty and its size is less than batch_size
    if batch:
        yield batch


def create_records_from_features(features_list, layer, batch_size=5000):
    for features in features_list:
        records = list()
        invalid = list()
        for i, f in enumerate(features):
            # create geo geometry object
            try:
                geos_geometry = geometry_collection_from_string(
                    json.dumps(f.get("geometry"))
                )
                wkb_writer = WKBWriter()
                geos_geometry = geometry_collection_from_string(
                    wkb_writer.write(geos_geometry)
                )
                geometry = (
                    geos_geometry if geos_geometry.valid else geos_geometry.buffer(0)
                )
            except GEOSException as e:
                logger.debug(f"Invalid geometry with error: {e}")
                invalid.append((i, f))
                continue

            if not geometry.valid:
                invalid.append((i, f))
                continue

            # create a record object
            properties = f.get("properties")
            records.append(
                Record(
                    layer=layer,
                    geometry=geometry,
                    source_properties=properties,
                    data=properties,
                    map_data=properties,
                )
            )
        if records:
            Record.objects.bulk_create(objs=records, batch_size=batch_size)
        logger.debug(
            f"Layer PK: {layer.id} // KEY: {layer.key} // Invalid Records Count:{len(invalid)}"
        )
        logger.debug(
            f"Layer PK: {layer.id} // KEY: {layer.key} // Invalid Records:{invalid}"
        )
        logger.debug(
            f"Layer PK: {layer.id} // KEY: {layer.key} // Created records Count: {len(records)}"
        )
        logger.debug("-" * 50)


def get_features_count_from_geopackage(file_object: File):
    """Get the number of features in a GeoPackage layer."""
    with MemoryFile(file_object.read()) as memory_file:
        for layer_name in memory_file.listlayers():
            with memory_file.open(layer=layer_name) as layer:
                return len(list(layer))
