# Generated by Django 3.2.25 on 2025-05-04 17:10

from django.db import migrations


def update_layers_with_jsonschema_columns(apps, schema_editor):
    Layer = apps.get_model("layers", "Layer")
    layers = Layer.objects.filter(json_schema__isnull=False).only(
        "id", "json_schema", "data"
    )
    for layer in layers:
        layer.data["columns"] = list(layer.json_schema["properties"].keys())
    Layer.objects.bulk_update(objs=layers, fields=["data"])


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0019_record_weight"),
    ]

    operations = [
        migrations.RunPython(
            update_layers_with_jsonschema_columns, migrations.RunPython.noop
        )
    ]
