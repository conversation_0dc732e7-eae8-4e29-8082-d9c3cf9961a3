import json
import os

from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import BaseTestMixin, UserFactory, WorkspaceRequestFactory
from layers.models import Layer
from workspaces.models import (
    WorkspaceRequestChoices,
    RequestTypeChoices,
    Workspace,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestDesignLayerJsonSchema(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace request to use in the tests
        self.workspace_request = WorkspaceRequestFactory(
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Test Design Layer",
                "description": "Test Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        self.mutation = """
        mutation MyMutation($workspaceRequestId: Int!, $jsonSchema: JSONString!, $summaryFields: JSONString!, $orgId: Int!) {
          designLayerJsonSchema(
            dataInput: {workspaceRequestId: $workspaceRequestId, jsonSchema: $jsonSchema, summaryFields: $summaryFields, orgId: $orgId}
          ) {
            workspace {
              id
              name
              description
            }
          }
        }
        """

        # Sample JSON schema for testing
        self.json_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string", "title": "Name"},
                "age": {"type": "integer", "title": "Age"},
                "address": {"type": "string", "title": "Address"},
            },
        }

        # Sample summary fields for testing
        self.summary_fields = ["name", "age"]

        self.variables = {
            "workspaceRequestId": self.workspace_request.id,
            "jsonSchema": json.dumps(self.json_schema),
            "summaryFields": json.dumps(self.summary_fields),
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot create a design layer JSON schema."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_missing_required_fields(self):
        """Test that required fields are validated."""
        # Remove required fields
        variables_without_json_schema = self.variables.copy()
        del variables_without_json_schema["jsonSchema"]

        response = self.client.execute(
            self.mutation,
            variables=variables_without_json_schema,
            context=self.auth_request,
        )
        # Check if the query fails with a required field error
        self.assertIn("errors", response)
        self.assertIn("jsonschema", response["errors"][0]["message"].lower())

    def test_invalid_workspace_request_id(self):
        """Test that an invalid workspace request ID is handled properly."""
        # Use a non-existent workspace request ID
        invalid_variables = self.variables.copy()
        invalid_variables["workspaceRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a workspace request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceRequestId": "Dataset with 99999 not found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_invalid_json_schema(self):
        """Test that an invalid JSON schema is handled properly."""
        # Create an invalid JSON schema
        invalid_variables = self.variables.copy()
        invalid_json_schema = {
            "type": "invalid_type",  # Invalid schema type
            "properties": {},
        }
        invalid_variables["jsonSchema"] = json.dumps(invalid_json_schema)

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a JSON schema error
        self.assertIn("errors", response)
        self.assertIn("jsonschema", str(response["errors"][0]["extensions"]).lower())

    def test_other_user_workspace_request_regular_user(self):
        """Test that a regular user cannot create a JSON schema for another user's workspace request."""
        # Create a workspace request owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        # Try to create a JSON schema for the other user's workspace request
        other_variables = self.variables.copy()
        other_variables["workspaceRequestId"] = other_workspace_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {other_workspace_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_workspace_request_superuser(self):
        """Test that a superuser can create a JSON schema for another user's workspace request."""
        # Create a workspace request owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        # Try to create a JSON schema for the other user's workspace request as a superuser
        other_variables = self.variables.copy()
        other_variables["workspaceRequestId"] = other_workspace_request.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)

        # Verify the workspace was created
        data = response["data"]["designLayerJsonSchema"]["workspace"]
        workspace = Workspace.objects.get(id=data["id"])
        self.assertTrue(workspace)

        # Verify the layer was created
        self.assertTrue(Layer.objects.filter(workspace=workspace).exists())

        # Verify the workspace request is now finished
        other_workspace_request.refresh_from_db()
        self.assertEqual(
            other_workspace_request.status, WorkspaceRequestChoices.FINISHED
        )

        # Verify the layer is associated with the workspace request
        self.assertIsNotNone(other_workspace_request.layer)

    def test_successful_creation(self):
        """Test successful creation of a design layer JSON schema."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the workspace was created
        data = response["data"]["designLayerJsonSchema"]["workspace"]
        workspace = Workspace.objects.get(id=data["id"])
        self.assertEqual(workspace.name, self.workspace_request.layer_data["title"])

        # Verify the layer was created
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.title, self.workspace_request.layer_data["title"])

        # Verify the workspace request is now finished
        self.workspace_request.refresh_from_db()
        self.assertEqual(
            self.workspace_request.status, WorkspaceRequestChoices.FINISHED
        )

        # Verify the layer is associated with the workspace request
        self.assertEqual(self.workspace_request.layer, layer)
