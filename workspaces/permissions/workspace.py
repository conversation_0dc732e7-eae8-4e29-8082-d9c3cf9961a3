from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Forbidden

from common.interfaces import PermissionsInterface
from common.utils import authorize_user
from organizations.models import Organization
from organizations.perms_constants import (
    CHANGE_WORKSPACE,
    DELETE_WORKSPACE,
    ADD_WORKSPACE,
)
from users.models import User
from workspaces.models import WorkspaceRequest


class WorkspacePermissions(PermissionsInterface):
    def check_permissions(
        self, user: User, organization: Organization, context: dict
    ) -> None:
        authorize_user(model_obj=organization, user=user, permission=CHANGE_WORKSPACE)


class DeleteWorkspacePermissions(PermissionsInterface):
    def check_permissions(
        self, user: User, organization: Organization, context: dict
    ) -> None:
        authorize_user(model_obj=organization, user=user, permission=DELETE_WORKSPACE)


class CreateWorkspacePermissions(PermissionsInterface):
    def check_permissions(
        self, user: User, organization: Organization, context: dict
    ) -> None:
        authorize_user(model_obj=organization, user=user, permission=ADD_WORKSPACE)
        dataset_request: WorkspaceRequest = context["dataset_request"]
        permissions = (dataset_request.created_by == user, user.is_superuser)
        if not any(permissions):
            raise Forbidden(
                reason={
                    "user": _(
                        "Permission denied, you do not have permission to this action."
                    )
                    % {}
                }
            )


class CreateEmptyWorkspacePerms(PermissionsInterface):
    def check_permissions(
        self, user: User, organization: Organization, context: dict
    ) -> None:
        authorize_user(model_obj=organization, user=user, permission=ADD_WORKSPACE)
