from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from organizations.models import Organization


class GeoServerProxySerializer(serializers.Serializer):
    SERVICE = serializers.CharField(default="WMS")
    VERSION = serializers.CharField(default="1.3.0")
    REQUEST = serializers.CharField(default="GetMap")
    LAYERS = serializers.CharField()
    ORGANIZATION = serializers.CharField()
    WIDTH = serializers.IntegerField()
    HEIGHT = serializers.IntegerField()
    BBOX = serializers.CharField()
    FORMAT = serializers.CharField()
    FORMAT_OPTIONS = serializers.CharField(required=False)
    TIME = serializers.CharField(required=False)
    CRS = serializers.CharField(required=False)
    TRANSPARENT = serializers.CharField(required=False)
    layer_id = serializers.IntegerField(required=False)
    CQL_FILTER = serializers.Char<PERSON><PERSON>(required=False)
    workspace_id = serializers.IntegerField()
    organization = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.only("id")
    )

    def validate(self, attrs):
        if attrs.get("CQL_FILTER") and not attrs.get("layer_id"):
            raise serializers.ValidationError(
                {"layer_id": _("layer_id is required when CQL_FILTER is provided")}
            )
        return attrs
