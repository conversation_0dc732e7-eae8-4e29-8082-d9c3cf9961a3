# Generated by Django 3.2.25 on 2025-07-20 08:11

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0016_remove_organization_users"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="organization",
            options={
                "permissions": (
                    ("view_user", "Can view user"),
                    ("add_user", "Can add user"),
                    ("change_user", "Can change user"),
                    ("delete_user", "Can delete user"),
                    ("view_layer", "Can view layer"),
                    ("add_layer", "Can add layer"),
                    ("delete_layer", "Can delete layer"),
                    ("edit_layer", "Can edit layer"),
                    ("view_record", "Can view record"),
                    ("add_record", "Can add record"),
                    ("delete_record", "Can delete record"),
                    ("edit_record", "Can edit record"),
                    ("add_workspace", "Can add workspace"),
                    ("change_workspace", "Can change workspace"),
                    ("delete_workspace", "Can delete workspace"),
                    ("view_workspace", "Can view workspace"),
                    ("add_role", "Can add role"),
                    ("change_role", "Can change role"),
                    ("delete_role", "Can delete role"),
                    ("view_role", "Can view role"),
                ),
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
            },
        ),
    ]
