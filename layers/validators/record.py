from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest, NotFound

from common.interfaces import InputValidation
from common.utils import validate_geometry
from layers.mixins import LayerMixin, RecordMixin
from layers.models import Record, Layer
from organizations.models import Organization


class CreateRecordValidator(InputValidation, LayerMixin, RecordMixin):
    def validate_and_get_data(
        self, data_input: dict, user, organization: Organization, **kwargs
    ) -> dict[str, Layer]:
        layer = self.get_layer_if_exists(data_input["layer_id"])
        self.validate_if_layer_read_only(layer)
        self.validate_form_data_schema(data_input["form_data"], layer.json_schema)
        self.check_valid_geometry(data_input["geometry"])
        return {"layer": layer}

    @staticmethod
    def check_valid_geometry(geometry):
        try:
            validate_geometry(geometry)
        except ValidationError as error:
            raise BadRequest(reason=error.message_dict)


class UpdateRecordValidator(InputValidation, RecordMixin, LayerMixin):
    def validate_and_get_data(
        self, data_input: dict, user, organization: Organization, **kwargs
    ) -> dict[str, Record]:
        record = self.get_record_if_exists(data_input["record_id"])
        self.validate_if_layer_read_only(record.layer)
        self.validate_form_data_schema(
            data_input["form_data"], record.layer.json_schema
        )
        return {"record": record}

    @staticmethod
    def get_record_if_exists(record_id: int):
        record = (
            Record.objects.filter(id=record_id)
            .select_related("layer__workspace")
            .first()
        )
        if not record:
            raise NotFound(
                reason={
                    "record_id": _("Record with id %(record_id)s not found")
                    % {"record_id": record_id}
                }
            )
        return record
