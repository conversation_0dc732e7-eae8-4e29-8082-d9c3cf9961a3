import json
from json import JSONDecodeError

from django.contrib.gis.db.models import Extent
from django.contrib.gis.gdal import Envelope
from django.contrib.gis.geos import GEOSGeometry, GeometryCollection
from django.contrib.gis.geos.prototypes.io import WKBWriter
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from shapely.geometry import mapping
from shapely.geometry.base import BaseGeometry


def validate_geometry(value):
    if value:
        """
        Validate and clean the 'geometry' field.
        """
        try:
            geometry = geometry_collection_from_string(value)
        except Exception:
            raise ValidationError({"geometry": _("Invalid geometry format") % {}})

        if not geometry.valid:
            raise ValidationError(
                {"geometry": _("Invalid geometry: Not a valid geometry object.") % {}}
            )

        return geometry


def geometry_collection_from_string(geom_string: str):
    # convert a geometry collection if required
    # expected types WKT, HEX, WKB, and GeoJSON
    geom = GEOSGeometry(geom_string)
    if geom.geom_type.lower() != "geometrycollection":
        geom = GeometryCollection([geom])
    return geom


def get_valid_geometry(geometry):
    geos_geometry = geometry_collection_from_string(json.dumps(geometry))
    if geos_geometry.hasz:
        wkb_writer = WKBWriter()
        geos_geometry = geometry_collection_from_string(wkb_writer.write(geos_geometry))
    geos_geometry = geos_geometry if geos_geometry.valid else geos_geometry.buffer(0)
    return geos_geometry


def convert_shapely_to_geos_geometry(shapely_geometry):
    """
    Converts a Shapely geometry object to a GeoDjango GEOSGeometry object.

    :param shapely_geometry: A Shapely geometry object (e.g., MultiPolygon, Polygon)
    :return: A GEOSGeometry object compatible with GeoDjango
    """
    # Step 1: Convert Shapely geometry to GeoJSON-like dictionary
    geojson_dict = mapping(shapely_geometry)

    # Step 2: Convert the dictionary to a GeoJSON string
    geojson_str = json.dumps(geojson_dict)

    # Step 3: Create a GEOSGeometry object from the GeoJSON string with specified SRID
    return GEOSGeometry(geojson_str, srid=4326)


def convert_shapely_geometries_to_geojson(data):
    """
    Convert any Shapely geometry objects in the 'data' lists to GeoJSON strings,
    regardless of the column name.
    """
    for item in data:
        # Check if 'data' is a list and contains GEOSGeometry objects
        if isinstance(item.get("data"), list):
            item["data"] = [
                mapping(element) if isinstance(element, BaseGeometry) else element
                for element in item["data"]
            ]
    return data


def convert_fiona_geometry_collection_to_geojson(geometry_collection):
    """Convert Fiona GeometryCollection to a valid GeoJSON dictionary."""
    if geometry_collection["type"] != "GeometryCollection":
        raise ValueError("Input geometry is not a GeometryCollection")

    # Convert each geometry in the collection
    geojson_geometries = []
    for geometry in geometry_collection["geometries"]:
        # Each geometry is a Fiona Geometry object; convert to GeoJSON
        geojson_geometries.append(geometry.__geo_interface__)

    # Return as a GeoJSON dictionary
    return {"type": "GeometryCollection", "geometries": geojson_geometries}


def calculate_boundaries_postgis(records):
    records_extent = records.aggregate(extent=Extent("geometry"))["extent"]
    if records_extent is not None:
        return GEOSGeometry(Envelope(records_extent).wkt)


def safe_geojson_loader(file_obj):
    data = json.load(file_obj)  # First try normal loading

    # Fix problematic features
    for feature in data["features"]:
        for key, value in feature["properties"].items():
            if isinstance(value, str):
                try:
                    # Try to parse string values that might be JSON
                    feature["properties"][key] = json.loads(value)
                except (JSONDecodeError, TypeError):
                    # Leave as-is if not valid JSON
                    pass
    return data
