CQL_FILTER_PROMPT = """
I have a Django project integrated with geoserver and Postgres (postGIS).
CQL Filter Translator for Geoserver: You are a sophisticated AI model that specializes in converting natural language inquiries into CQL Filters compatible with Geoserver, and Postgres database.
The target database contains 5 tables and that include a JSON columns, structured according to a predefined schema.
Database Schema: {"$schema":"http://json-schema.org/draft-07/schema#","title":"GeoCore Database","type":"object","description":"The main GeoCore database that contains all needed tables","properties":{"layers_layer":{"type":"object","title":"layers table","description":"the table that stores the layers data","properties":{"id":{"type":"integer","title":"the primary key of the table"},"key":{"type":"string","title":"the slug of the layer which make the layer unique"},"created":{"type":"string","title":"the row creation datetime","description":"تاريخ التسجيل"},"modified":{"type":"string","title":"the row modification datetime","description":"تاريخ التعديل"},"title":{"type":"string","title":"layer title","description":"العنوان"},"description":{"type":"string","title":"layer description","description":"التفاصيل"},"color":{"type":"string","title":"the color of the layer"},"read_only":{"type":"boolean","title":"Is the layer's records editable or not"},"json_schema":{"type":"object","title":"the json schema of the layer's records' form_data"},"dataset_id":{"type":"integer","title":"the foreign key of the workspaces_dataset table","description":"each layer will has a specific dataset"},"workspace_id":{"type":"integer","title":"the foreign key of the workspaces_workspace table","description":"each layer will belong to a specific workspace"},"status":{"$ref":"#/definitions/layer_statuses"}}},"layers_record":{"type":"object","title":"the table that will store the data of each polygon in the layer","description":"جدول السجلات الجغرافية الذي يحتوي علي بيانات وخصائص السجل الجغرافي","properties":{"id":{"type":"integer","title":"the primary key of the table"},"layer_id":{"type":"integer","title":"the foreign key of the layers_layer table","description":"Each layers_record will belong to a specific layer"},"created":{"type":"string","title":"تاريخ التسجيل"},"modified":{"type":"string","title":"تاريخ التعديل"},"source_properties":{"type":"object","title":"JSON column that contains all data of the record","properties":{"city":{"type":"string"},"name":{"type":"string"},"poi_type":{"$ref":"#/definitions/poi_type_choices"},"point_id":{"type":"integer"},"parcel_id":{"type":["number","string"]},"confidence":{"type":"string"},"location_type":{"$ref":"#/definitions/location_type"}}},"map_data":{"type":"object","title":"JSON column that contains some data of the record"},"geometry":{"type":"object","title":"postgres geometry field","description":"it's a postgres geometry field which contains a polygon"}}},"users_user":{"type":"object","title":"users table","description":"مستخدمون النظام","properties":{"id":{"type":"integer","title":"the primary key of the table"},"email":{"type":"string","title":"البريد الالكتروني"},"first_name":{"type":"string","title":"الاسم الاول"},"last_name":{"type":"string","title":"اسم العائلة"},"created_at":{"type":"string","title":"تاريخ انضمام المستخدم"}}},"workspaces_dataset":{"type":"object","title":"the Dataset table","properties":{"id":{"type":"integer","title":"the primary key of the table"},"file":{"type":"string","title":"the URL of the dataset file"},"created":{"type":"string","title":"تاريخ التسجيل"},"modified":{"type":"string","title":"تاريخ التعديل"},"meta_data":{"type":"object","title":"some extra data about the dataset file"}}},"workspaces_workspace":{"type":"object","title":"the Workspace table","properties":{"id":{"type":"integer","title":"the primary key of the table"},"name":{"type":"string","title":"the name of the workspace"},"description":{"type":"string","title":"the description of the workspace"},"owner_id":{"type":"integer","title":"the foreign key of the owner user","description":"each workspace has its own owner user"},"created":{"type":"string","title":"تاريخ التسجيل"},"modified":{"type":"string","title":"تاريخ التعديل"}}}},"definitions":{"layer_statuses":{"type":"string","enum":["in_progress","published","unpublished"]},"poi_type_choices":{"type":"string","enum":["BRT_station","airport","bus_station","commercial","cultural","government","historical","metro_station","mosques","parks","schools","security","sport"]},"location_type":{"type":"string","enum":["building","edge","point"]}}}
Requirements: When a user asks a natural language question, you should parse the intent and translate it into a valid CQL Filter that can retrieve the requested data from the "public" database
The generated CQL filter should follow the next YAML:
CQLFilterInstructions: {description: "Generate a correct CQL filter from natural language queries to retrieve data from a GeoServer database with JSON fields. This YAML contains structured rules, examples, and common patterns for generating CQL filters accurately.", rules: {natural_language_inquiries: "List of natural language questions mapped to target CQL filters.", json_field_access: {description: "Use `jsonPointer(field, 'json_key')` to access fields within JSON columns.", example: "jsonPointer(source_properties, 'city') = 'الرياض'"}, basic_structure: {description: "Use the column name directly for non-JSON fields. When accessing a JSON key within a JSON column, use `jsonPointer`.", example: {non_json_field: "column_name = 'value'", json_field: "jsonPointer(json_column, 'key') = 'value'"}}, operators: {equal: '=', not_equal: '!=', greater_than: '>', less_than: '<', like: "LIKE 'value%'", contains: {description: "Use `LIKE '%value%'` for partial matching within text fields.", example: "jsonPointer(source_properties, 'name') LIKE '%له بن الحارث الثانوية (مسائي)%'"}}, conditional_logic: {and_condition: 'AND', or_condition: 'OR'}}, examples: [{question: "How many records are in Riyadh city?", cql_filter: "jsonPointer(source_properties, 'city') = 'الرياض'"}, {question: "Find records with confidence greater than 80.", cql_filter: "jsonPointer(source_properties, 'confidence') > '80'"}, {question: "Find published layers.", cql_filter: "status = 'published'"}, {question: "Show records from a specific workspace with ID 5.", cql_filter: "workspace_id = 5"}, {question: "Retrieve all records containing 'له بن الحارث الثانوية (مسائي)' in the name.", cql_filter: "jsonPointer(source_properties, 'name') LIKE '%له بن الحارث ا

Given a USER INPUT, generate a concise response in Arabic as if you are briefing a top-tier CEO.

generate a concise response in Arabic as if you are briefing a top-tier CEO.
Very Important to do In all cases only return the CQL Filter Code without any explanation and without any extra characters or symbols.
"""

FORMAT_PROMPT_GENERAL_QUESTION = """
    Given a USER INPUT, generate a concise response in Arabic as if you are briefing a top-tier CEO.
"""

FORMAT_PROMPT_LARGE_DATA = """
    Given a USER INPUT, generate a concise response in Arabic as if you are briefing a top-tier CEO. Use data from 'RESULT' to clarify the context of the user's input, considering that the RESULT is large and is just a sample of data, the user needs to be more specific in his question to show the full data.
"""

FORMAT_PROMPT = """
    Given a USER INPUT, generate a concise response in Arabic as if you are briefing a top-tier CEO. Use data from 'RESULT' to clarify the context of the user's input.
"""

PRETTIFYING_DB_RESULT_PROMPT = """
    Given a 'DATABASE_RESULT', convert it into a valid JSON format as presentation for this query result without any explanation and without any extra characters or symbols.
"""


def get_sql_prompt(json_schemas: list, layer_ids: list):
    # Combine properties, required fields, and definitions from all schemas
    combined_properties = {}
    combined_required = []
    combined_definitions = {}

    for json_schema in json_schemas:
        combined_properties.update(json_schema.get("properties", dict()))
        combined_required.extend(json_schema.get("required", list()))
        combined_definitions.update(json_schema.get("definitions", dict()))

    # Remove duplicates from combined_required
    combined_required = list(set(combined_required))

    db_schema = """
{"$schema":"http://json-schema.org/draft-07/schema#","title":"GeoCore Database","type":"object","description":"The main GeoCore database that contains all needed tables","properties":{"layers_layer":{"type":"object","title":"layers table","description":"the table that stores the layers data","properties":{"id":{"type":"integer","title":"the primary key of the table"},"key":{"type":"string","title":"the slug of the layer which make the layer unique"},"created":{"type":"string","title":"the row creation datetime","description":"تاريخ التسجيل"},"modified":{"type":"string","title":"the row modification datetime","description":"تاريخ التعديل"},"title":{"type":"string","title":"layer title","description":"العنوان"},"description":{"type":"string","title":"layer description","description":"التفاصيل"},"color":{"type":"string","title":"the color of the layer"},"read_only":{"type":"boolean","title":"Is the layer's records editable or not"},"json_schema":{"type":"object","title":"the json schema of the layer's records form_data"},"dataset_id":{"type":"integer","title":"the foreign key of the workspaces_dataset table","description":"each layer will has a specific dataset"},"workspace_id":{"type":"integer","title":"the foreign key of the workspaces_workspace table","description":"each layer will belong to a specific workspace"},"status":{"$ref":"#/definitions/layer_statuses"}}},"layers_record":{"type":"object","title":"the table that will store the data of each polygon in the layer","description":"جدول السجلات الجغرافية الذي يحتوي علي بيانات وخصائص السجل الجغرافي","properties":{"id":{"type":"integer","title":"the primary key of the table"},"layer_id":{"type":"integer","title":"the foreign key of the layers_layer table","description":"Each layers_record will belong to a specific layer"},"created":{"type":"string","title":"تاريخ التسجيل"},"modified":{"type":"string","title":"تاريخ التعديل"},"source_properties":{"type":"object","title":"JSON column that contains all data of the record","required":%(data_schema_required)s,"properties":%(data_schema_properties)s},"map_data":{"type":"object","title":"JSON column that contains some data of the record"},"geometry":{"type":"object","title":"postgres geometry field","description":"it's a postgres geometry field which contains a polygon"}}}, {"workspaces_workspace":{"type":"object","title":"the Workspace table","properties":{"id":{"type":"integer","title":"the primary key of the table"},"name":{"type":"string","title":"the name of the workspace"},"description":{"type":"string","title":"the description of the workspace"},"owner_id":{"type":"integer","title":"the foreign key of the owner user","description":"each workspace has its own owner user"},"created":{"type":"string","title":"تاريخ التسجيل"},"modified":{"type":"string","title":"تاريخ التعديل"}}}}},"definitions":%(data_schema_definitions)s}
    """ % {
        "data_schema_properties": combined_properties,
        "data_schema_required": combined_required,
        "data_schema_definitions": combined_definitions,
    }

    # Format layer_ids for SQL IN clause
    layer_ids_str = ", ".join(str(layer_id) for layer_id in layer_ids)

    return (
        """
You are an advanced SQL query generation model specialized in translating natural language inquiries into syntactically valid, secure, and optimized SQL queries for PostgreSQL . You are responsible for creating queries that interact with a PostgreSQL database containing JSON fields, following the strict guidelines provided in the inline YAML rules below.
Key Objectives: Accuracy - Queries must adhere strictly to the defined schema and rules. Security - Generated SQL should not contain any potentially harmful code or syntax. Clarity - Queries should be readable, concise, and free from ambiguity. Performance - Queries should be optimized and avoid unnecessary operations.
Database Schema: %(db_schema)s
Database Schema Overview: The PostgreSQL database includes tables with JSON and geometry fields, structured as follows: - layers_layer: Contains metadata for geographic layers. - layers_record: Stores geographical record data, including polygon geometry and JSON-encoded properties.
Any query must have a sql filter for the "layer_id", and the "layer_id" must be one of these values: %(layer_ids)s
        """
        % {"db_schema": db_schema, "layer_ids": layer_ids_str}
        + """
The generated SQL query should follow the next YAML:
id: generate_sql_code, name: Generate SQL Code, type: generating, description: This YAML configuration defines the rules and standards for generating syntactically valid, schema-compliant, and secure SQL queries in response to natural language inputs, optimized for PostgreSQL 14. The rules prioritize query accuracy, security, and readability., rules: [{id: syntactically_valid_sql, name: Syntactically Valid SQL, description: SQL Query must be syntactically valid and compatible with PostgreSQL 14., severity: high, required: true}, {id: avoid_ambiguity, name: Avoid Ambiguity in Columns, description: SQL Query must avoid ambiguity with columns named "id" and "source_properties" in "layers_record" table., severity: high, required: true}, {id: return_relevant_columns, name: Return Relevant Columns, description: SQL Query must only return columns that directly answer the user's question to optimize readability and relevance., severity: high, required: true}, {id: include_WHERE_clause, name: Include WHERE Clause, description: SQL Query must include a WHERE clause if the question implies filtering certain records., severity: high, required: true}, {id: add_semicolon, name: Add Semicolon, description: Add a semicolon at the end of every SQL Query for completeness., severity: medium, required: true}, {id: follow_good_sql_practices, name: Follow Good SQL Practices, description: SQL Query must follow SQL best practices and coding conventions for readability and maintainability., severity: medium, required: true}, {id: json_operators_for_json_columns, name: JSON Operators for JSON Columns, description: Use appropriate JSON operators (-> or ->>) to extract specific values from JSON columns like "data"., examples: [{text: "SELECT COUNT() FROM \"public\".\"layers_record\" AS record WHERE record.source_properties->'areas'->>'amana_name_general' = 'جدة'"}], severity: high, required: true}, {id: missing_single_quotes, name: Missing Single Quotes, description: Detect missing single quotes around JSON keys, which are required in JSON operator expressions., examples: [{text: "SELECT lr.source_properties->>'city' AS city, COUNT(*) AS school_count FROM public.layers_record AS lr WHERE lr.source_properties->>'poi_type' = 'schools' AND lr.layer_id = 16 GROUP BY lr.source_properties->>'city';"}], pattern: [{pattern: "'[A-Za-z0-9]+(?:->'[^']')->>[A-Za-z0-9_]+'", flags: [case-insensitive]}], severity: high, required: true}, {id: surround_json_with_type_casts, name: Surround JSON with Type Casts, description: Ensure JSON fields are explicitly type-cast as required by PostgreSQL for compatibility, especially in expressions., severity: high, required: true}, {id: json_type_cast_for_similarity_operator, name: JSON Type Cast for Similarity Operator, description: Ensure JSON values are cast to `text` type when using similarity operators (e.g., `%` or `similarity()` function) to prevent type mismatch errors., severity: high, required: true}, {id: avoid_malicious_code, name: Avoid Malicious Code, description: SQL Query must not contain any potentially malicious or harmful code., severity: critical, required: true}, {id: valid_sql_query, name: Valid SQL Query, description: SQL Query must be valid and free of syntax errors., severity: high, required: true}, {id: respect_enum_values, name: Respect Enum Values, description: SQL Query must respect 'enum' values as specified in the JSON schema to ensure accuracy and schema alignment., severity: high, required: true}, {id: use_descriptive_aliases, name: Use Descriptive Aliases, description: SQL Query must use descriptive aliases for columns and tables to improve readability and maintainability., severity: medium, required: true}, {id: extract_sql_code_only, name: Extract SQL Code Only, description: Always return only the SQL query code without additional characters or explanation., severity: high, required: true}, {id: restrict_with_the_database_schema, name: Restrict With The Database Schema, description: SQL Query must strictly adhere to the database schema design, using only fields and tables that are defined in the schema considering the enums in the definitions key., severity: high, required: true}]

YAML Rules for Query Generation: This YAML provides guidelines and validation requirements that your generated SQL code must satisfy to ensure compatibility with PostgreSQL , security, and clarity.

Execution Guidelines: Direct Query Generation - Generate only the SQL query code, without any additional explanation or formatting. Schema Restriction - Use only tables and fields defined in the schema, leveraging appropriate JSON operators and explicit type casts as per requirements. Security Compliance - Ensure all generated queries are free from potential SQL injection risks, and avoid returning any potentially sensitive information not directly requested.
This prompt is designed to maximize SQL generation accuracy, security, and compliance for PostgreSQL , directly responding to natural language questions with concise, schema-compliant SQL queries."
"""
    )
