from django.contrib import admin

from workspaces.models import WorkspaceRequest


class WorkspaceRequestAdmin(admin.ModelAdmin):
    list_display = ["id", "status", "request_type", "created_by", "created"]
    list_display_links = ["id", "status"]
    list_filter = ["status", "request_type"]
    search_fields = ["created_by__phone", "created_by__email"]

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(WorkspaceRequest, WorkspaceRequestAdmin)
