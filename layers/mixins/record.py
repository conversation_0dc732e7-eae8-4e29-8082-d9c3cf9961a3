from django.core.exceptions import ValidationError
from gabbro.graphene import BadRequest

from common.utils import draft7_validate


class RecordMixin:
    @staticmethod
    def validate_form_data_schema(form_data: dict, json_schema: dict):
        try:
            draft7_validate(json_schema, form_data, "form_data")
        except ValidationError as error:
            raise BadRequest(reason=error.message_dict)
