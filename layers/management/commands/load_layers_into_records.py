import logging
import time

from django.core.management import BaseCommand
from django.core.management.base import CommandError

from layers.management.commands.load_old_tataba_data import (
    load_layers_records_with_parallel_processing,
    fill_datasets_and_layers_with_geocore_data,
)
from layers.models import Layer
from organizations.models import Organization
from workspaces.models import Workspace

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("--organization_id", type=int, required=True)
        parser.add_argument(
            "--replace",
            nargs="?",
            const=True,
            default=False,
            type=lambda x: str(x).lower() in ("yes", "true", "t", "y", "1"),
        )
        parser.add_argument("--batch_size", type=int, default=5000)

    def handle(self, *args, **options):
        start = time.time()
        logger.debug("Loading layers...")
        logger.debug("Starting ......")
        batch_size = options.get("batch_size")
        organization = Organization.objects.filter(
            id=options.get("organization_id")
        ).first()
        if not organization:
            raise CommandError("The organization_id must be a valid id")
        unloaded_layers_ids = organization.workspaces_data["unloaded_layers_ids"]
        layers = Layer.objects.filter(id__in=unloaded_layers_ids).order_by("id")
        logger.debug(f"Loading {len(layers)} Layers")
        load_layers_records_with_parallel_processing(
            layers=layers, organization=organization, batch_size=batch_size
        )
        workspaces = Workspace.objects.prefetch_related("layers__dataset")
        fill_datasets_and_layers_with_geocore_data(workspaces)
        end = time.time()
        logger.debug(f"All Layers Loaded in {end-start} seconds")
        logger.debug("Exiting ......")
