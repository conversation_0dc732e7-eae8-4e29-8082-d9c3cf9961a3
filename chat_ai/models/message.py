from django.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import JSONField


class Message(TimeStampedModel):
    conversation = models.ForeignKey(
        "chat_ai.Conversation",
        on_delete=models.CASCADE,
        related_name="messages",
        verbose_name=_("conversation"),
    )
    message = JSONField(
        verbose_name=_("message"),
        help_text=_("contains user input, GPT output, and SQL Query"),
    )

    class Meta:
        verbose_name = _("Message")
        verbose_name_plural = _("Messages")

    def __str__(self):
        return f"conversation: {self.conversation.pk}"
