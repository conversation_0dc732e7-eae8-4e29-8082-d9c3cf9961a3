# Generated by Django 3.2.25 on 2025-04-27 17:59

from django.db import migrations
from gabbro.layers_engine.enums import LayerStatus

from workspaces.models import EDAReportSourceChoices


def reshape_eda_report_data_in_datasets(apps, schema_editor):
    Dataset = apps.get_model("workspaces", "Dataset")
    EDAReport = apps.get_model("workspaces", "EDAReport")
    datasets = Dataset.objects.filter(
        meta_data__eda_report__status=LayerStatus.PUBLISHED.value
    )
    eda_reports = []
    for dataset in datasets:
        eda_report_data: dict = dataset.meta_data.pop("eda_report")
        eda_report_data["source"] = EDAReportSourceChoices.YDATA.value
        dataset.meta_data["eda_reports"] = [eda_report_data]
        layers = dataset.layers.filter(eda_reports__isnull=True)
        for layer in layers:
            eda_reports.append(
                EDAReport(
                    layer=layer,
                    hash_code=eda_report_data["hash"],
                    file=eda_report_data["report_path"],
                    created=eda_report_data["created_at"],
                    source=eda_report_data["source"],
                )
            )
    if eda_reports:
        EDAReport.objects.bulk_create(eda_reports)
    if datasets:
        Dataset.objects.bulk_update(objs=datasets, fields=["meta_data"])


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0019_edareport_source"),
    ]

    operations = [
        migrations.RunPython(
            reshape_eda_report_data_in_datasets, migrations.RunPython.noop
        )
    ]
