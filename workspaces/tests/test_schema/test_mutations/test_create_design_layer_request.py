import os

from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import BaseTestMixin, UserFactory, WorkspaceRequestFactory
from workspaces.models import (
    WorkspaceRequest,
    WorkspaceRequestChoices,
    RequestTypeChoices,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestCreateDesignLayerRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation MyMutation($title: String!, $description: String, $color: String!, $orgId: Int!, $readOnly: Boolean) {
          createDesignLayerRequest(
            dataInput: {title: $title, description: $description, color: $color, orgId: $orgId, readOnly: $readOnly}
          ) {
            workspaceRequest {
              id
              status
              requestType
              layerData
              createdBy {
                id
                email
              }
            }
          }
        }
        """
        self.variables = {
            "title": "Test Design Layer",
            "description": "Test Description",
            "color": "#e3cec7",
            "orgId": self.organization.id,
            "readOnly": False,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot create a design layer request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_missing_required_fields(self):
        """Test that required fields are validated."""
        # Remove required fields
        variables_without_title = self.variables.copy()
        del variables_without_title["title"]

        response = self.client.execute(
            self.mutation, variables=variables_without_title, context=self.auth_request
        )
        # Check if the query fails with a required field error
        self.assertIn("errors", response)
        self.assertIn("title", response["errors"][0]["message"])

    def test_existing_in_progress_request(self):
        """Test that a user cannot create a new design layer request if they already have one in progress."""
        # Create an existing in-progress design layer request for the user
        existing_request = WorkspaceRequestFactory(
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Existing Design Layer",
                "description": "Existing Description",
                "color": "#000000",
            },
        )

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query fails with an error about existing request
        self.assertIn("errors", response)
        self.assertIn(
            "You have Design Layer request in progress",
            str(response["errors"][0]["extensions"]),
        )

    def test_other_user_request_superuser(self):
        """Test that a superuser cannot create a design layer request if another user has one in progress in the same organization."""
        # Create an existing in-progress design layer request for another user
        other_user = UserFactory(is_superuser=False)
        existing_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
            },
        )

        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query fails with an error about existing request
        self.assertIn("errors", response)
        self.assertIn(
            "You have Design Layer request in progress",
            str(response["errors"][0]["extensions"]),
        )

    def test_successful_creation(self):
        """Test successful creation of a design layer request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["createDesignLayerRequest"]["workspaceRequest"]
        self.assertEqual(
            data["status"], WorkspaceRequestChoices.IN_PROGRESS.value.upper()
        )
        self.assertEqual(
            data["requestType"], RequestTypeChoices.DESIGN_LAYER.value.upper()
        )
        self.assertEqual(int(data["createdBy"]["id"]), self.user.id)

        # Verify the layer data
        layer_data = data["layerData"]
        self.assertEqual(layer_data["title"], self.variables["title"])
        self.assertEqual(layer_data["description"], self.variables["description"])
        self.assertEqual(layer_data["color"], self.variables["color"])
        self.assertEqual(layer_data["read_only"], self.variables["readOnly"])

        # Verify the database record
        workspace_request = WorkspaceRequest.objects.get(id=data["id"])
        self.assertEqual(workspace_request.status, WorkspaceRequestChoices.IN_PROGRESS)
        self.assertEqual(
            workspace_request.request_type, RequestTypeChoices.DESIGN_LAYER
        )
        self.assertEqual(workspace_request.created_by, self.user)
        self.assertEqual(workspace_request.organization, self.organization)
