from typing import TypedDict, Optional, Text, Union


class MessageData(TypedDict):
    """
    Schema for ChatGPT Conversation.

    user_input: the user input for ChatGPT
    sql_query: the SQL query that will be returned from ChatGPT
    sql_result: the result of db after running the SQL query
    final_result: the result of ChatGPT after representing the SQL output
    """

    user_input: Text
    sql_query: Optional[Text]
    sql_result: Optional[Union[list, str]]
    final_result: Optional[Text]
