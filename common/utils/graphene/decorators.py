from functools import wraps
from typing import List

from django.db.models import Model
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Unauthorized, NotFound, Forbidden

from common.utils import get_value_from_dict
from organizations.models import Organization
from users.models import User, ActiveStatusChoices


def authentication_required(func):
    @wraps(func)
    def wrapper(root, info, *args, **kwargs):
        user = info.context.user
        if not user.is_authenticated:
            raise Unauthorized()
        if user.active_status == ActiveStatusChoices.DELETED:
            raise Forbidden(reason={"user": _("Your account is deleted") % {}})
        if user.active_status == ActiveStatusChoices.INACTIVE:
            raise Forbidden(reason={"user": _("Your account is inactive") % {}})
        return func(root, info, *args, **kwargs)

    return wrapper


def organization_required(func):
    """
    Ensures that the context (request) contains a valid organization for the query execution.
    """

    @wraps(func)
    def wrapper(root, info, *args, **kwargs):
        org_id = get_value_from_dict(kwargs, "org_id")
        user = info.context.user
        organization = Organization.objects.filter(id=org_id).first()
        if not organization:
            raise NotFound(
                reason={
                    "org_id": _("Organization with id %(org_id)s not found")
                    % {"org_id": org_id}
                }
            )

        if (
            not user.is_superuser
            and not organization.acl_individuals.filter(id=user.id).exists()
        ):
            raise Unauthorized(
                reason={
                    "org_id": _("User %(user_id)s not in organization %(org_id)s")
                    % {"org_id": org_id, "user_id": user.id}
                }
            )
        setattr(info.context, "organization", organization)
        return func(root, info, *args, **kwargs)

    return wrapper


def authorize_user(model_obj, user: User, permission: str):
    if not user.has_perm(obj=model_obj, perm=permission) and not user.is_superuser:
        raise Forbidden(reason={"user": _("Permission denied") % {}})


def authorize_multiple_objects_for_user(
    models_objs: List[Model], perm: str, user: User
):
    forbiddings_count = 0
    for model_obj, permission in zip(models_objs, [perm] * len(models_objs)):
        try:
            authorize_user(model_obj=model_obj, user=user, permission=permission)
        except Forbidden:
            forbiddings_count += 1
    if forbiddings_count == len(models_objs):
        raise Forbidden(reason={"user": _("Permission denied") % {}})
