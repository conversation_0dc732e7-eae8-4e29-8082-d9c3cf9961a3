# Generated by Django 3.2.25 on 2025-07-06 13:54

import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models

import layers.models.typed_dict


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0020_update_layers_with_jsonschema_columns"),
    ]

    operations = [
        migrations.CreateModel(
            name="SLD",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="SLD Title")),
                (
                    "sld_type",
                    models.CharField(
                        choices=[("points", "Points"), ("heatmap", "Heatmap")],
                        default="points",
                        max_length=20,
                        verbose_name="SLD Type",
                    ),
                ),
                (
                    "feature_style",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True,
                        default=layers.models.typed_dict.get_default_sld_data,
                        verbose_name="SLD Data",
                    ),
                ),
                (
                    "xml_body",
                    models.TextField(blank=True, default="", verbose_name="XML Body"),
                ),
            ],
            options={
                "verbose_name": "SLD",
                "verbose_name_plural": "SLDs",
            },
        ),
        migrations.AddConstraint(
            model_name="sld",
            constraint=models.UniqueConstraint(
                fields=("title", "sld_type"), name="unique_sld_title_type"
            ),
        ),
        migrations.AddField(
            model_name="layer",
            name="slds",
            field=models.ManyToManyField(
                blank=True, related_name="layers", to="layers.SLD", verbose_name="SLDs"
            ),
        ),
    ]
