import graphene

from users.models import ActiveStatusChoices


class AddUserInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    email = graphene.String(required=True)
    phone = graphene.String(required=True)
    role_id = graphene.Int(required=True)
    first_name = graphene.String(required=True)
    last_name = graphene.String(required=True)


class ChangeUserRoleInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    user_id = graphene.Int(required=True)
    role_id = graphene.Int(required=True)


class RoleInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    title = graphene.String(required=True)
    permissions_list = graphene.List(graphene.Int, required=True)


class UpdateRoleInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    role_id = graphene.Int(required=True)
    title = graphene.String(required=False)
    permissions_list = graphene.List(graphene.Int, required=False)


class DeleteRoleInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    role_id = graphene.Int(required=True)


class ChangeUserActiveStatusInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    user_id = graphene.Int(required=True)
    active_status = graphene.Field(graphene.Enum.from_enum(ActiveStatusChoices))


class AssignWorkspaceUserPermissionsInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_id = graphene.Int(required=True)
    users_ids = graphene.List(graphene.Int, required=True)
    permissions_ids = graphene.List(graphene.Int, required=True)
