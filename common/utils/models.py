import os

from django.conf import settings
from django.core.validators import FileExtensionValidator

ALLOWED_DATASET_EXTENSIONS = ["csv", "gpkg", "geojson", "kml", "shp"]

dataset_extension_validator = FileExtensionValidator(
    allowed_extensions=ALLOWED_DATASET_EXTENSIONS
)


def dataset_path(instance, filename):
    return os.path.join("media", "original_datasets", filename)


def get_owner_user(organization):
    """
    Retrieves the owner user of the organization.
    """
    return organization.acl_individuals.filter(
        rule__role__codename=settings.MAIN_OWNER_ROLE_CODENAME
    ).first()
