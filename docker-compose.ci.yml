version: '3.7'
services:
  geocore-ci:
    build:
      context: .
      dockerfile: ./docker/ci/Dockerfile
    image: geocore-ci
    container_name: geocore-ci
    env_file:
      - .env
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    command: /start-tests.sh

  db:
    image: kartoza/postgis:14-3.1
    container_name: geocore-ci-db
    env_file:
      - .env
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql
volumes:
  postgres_data:
