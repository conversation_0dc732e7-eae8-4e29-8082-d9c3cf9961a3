import graphene

from common.service import Service
from common.utils import authentication_required, organization_required
from layers.permissions import (
    UpdateLayerPermissions,
    CreateLayerFromDatasetPermissions,
    DeleteLayerPermissions,
)
from layers.permissions.record import RecordPermissions
from layers.schema.input_object_types import (
    UpdateRecordsSummaryFieldsInputType,
    CreateRecordInputType,
    UpdateRecordInputType,
    UpdateLayerInputType,
    CreateLayerFromDatasetInputType,
    DeleteLayerInputType,
    DuplicateLayerInputType,
    HeatMapInputType,
    SLDInputType,
    UpdateLayerFiltersInputType,
)
from layers.schema.object_types import RecordType, LayerType, SLDType
from layers.strategies.layer import (
    UpdateLayerStrategy,
    CreateLayerFromDatasetStrategy,
    DeleteLayerStrategy,
    DuplicateLayerStrategy,
    LayerHeatMapStrategy,
    UpdateLayerSLDStrategy,
    UpdateLayerFiltersStrategy,
)
from layers.strategies.record import (
    UpdateRecordsSummaryFieldsStrategy,
    CreateRecordsStrategy,
    UpdateRecordStrategy,
)
from layers.validators import (
    LayerRecordSummaryValidator,
    CreateRecordValidator,
    UpdateRecordValidator,
    LayerValidator,
    CreateLayerFromDatasetValidator,
    DuplicateLayerValidator,
    HeatMapValidator,
    UpdateLayerSLDValidator,
)


class UpdateRecordsSummaryFields(graphene.Mutation):
    success = graphene.Boolean()

    class Input:
        data_input = UpdateRecordsSummaryFieldsInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateRecordsSummaryFieldsStrategy(),
            validator=LayerRecordSummaryValidator(),
            perms=UpdateLayerPermissions(),
        )
        service.handle(user=user, organization=organization, data_input=data_input)
        return UpdateRecordsSummaryFields(success=True)


class CreateRecord(graphene.Mutation):
    record = graphene.Field(RecordType)

    class Input:
        data_input = CreateRecordInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateRecordsStrategy(),
            validator=CreateRecordValidator(),
            perms=UpdateLayerPermissions(),
        )
        record = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateRecord(record=record)


class UpdateRecord(graphene.Mutation):
    record = graphene.Field(RecordType)

    class Input:
        data_input = UpdateRecordInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateRecordStrategy(),
            validator=UpdateRecordValidator(),
            perms=RecordPermissions(),
        )
        record = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateRecord(record=record)


class CreateLayerFromDataset(graphene.Mutation):
    layer = graphene.Field(LayerType)

    class Input:
        data_input = CreateLayerFromDatasetInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateLayerFromDatasetStrategy(),
            validator=CreateLayerFromDatasetValidator(),
            perms=CreateLayerFromDatasetPermissions(),
        )
        layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateLayerFromDataset(layer=layer)


class UpdateLayer(graphene.Mutation):
    layer = graphene.Field(LayerType)

    class Input:
        data_input = UpdateLayerInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerStrategy(),
            validator=LayerValidator(),
            perms=UpdateLayerPermissions(),
        )
        layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateLayer(layer=layer)


class DeleteLayer(graphene.Mutation):
    success = graphene.Boolean()

    class Input:
        data_input = DeleteLayerInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=DeleteLayerStrategy(),
            validator=LayerValidator(),
            perms=DeleteLayerPermissions(),
        )
        service.handle(user=user, organization=organization, data_input=data_input)
        return DeleteLayer(success=True)


class DuplicateLayer(graphene.Mutation):
    new_layer = graphene.Field(LayerType)

    class Input:
        data_input = DuplicateLayerInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=DuplicateLayerStrategy(),
            validator=DuplicateLayerValidator(),
            perms=UpdateLayerPermissions(),
        )
        new_layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return DuplicateLayer(new_layer=new_layer)


class CreateHeatMap(graphene.Mutation):
    sld = graphene.Field(SLDType)

    class Input:
        data_input = HeatMapInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=LayerHeatMapStrategy(),
            validator=HeatMapValidator(),
            perms=UpdateLayerPermissions(),
        )
        sld = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateHeatMap(sld=sld)


class CreateLayerSLD(graphene.Mutation):
    sld = graphene.Field(SLDType)

    class Input:
        data_input = SLDInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerSLDStrategy(),
            validator=UpdateLayerSLDValidator(),
            perms=UpdateLayerPermissions(),
        )
        sld = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateLayerSLD(sld=sld)


class UpdateLayerFilters(graphene.Mutation):
    layer = graphene.Field(LayerType)

    class Input:
        data_input = UpdateLayerFiltersInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerFiltersStrategy(),
            validator=LayerValidator(),
            perms=UpdateLayerPermissions(),
        )
        layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateLayerFilters(layer=layer)


class Mutation(graphene.ObjectType):
    update_records_summary_fields = UpdateRecordsSummaryFields.Field()
    create_record = CreateRecord.Field()
    update_record = UpdateRecord.Field()
    update_layer = UpdateLayer.Field()
    create_layer_from_dataset = CreateLayerFromDataset.Field()
    delete_layer = DeleteLayer.Field()
    duplicate_layer = DuplicateLayer.Field()
    create_heat_map = CreateHeatMap.Field()
    create_layer_sld = CreateLayerSLD.Field()
    update_layer_filters = UpdateLayerFilters.Field()
