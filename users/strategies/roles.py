from common.interfaces import Strategy
from organizations.models import Organization
from users.models import User


class ChangeUserRoleStrategy(Strategy):
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        user_target = context["user_target"]
        role = context["role"]
        # remove old rules of the user
        organization.acl_remove_user(user=user_target)
        organization.acl_add_user(user=user_target, roles=[role])
        user_target.organization = organization
        return user_target


class AssignWorkspaceUserPermissionsStrategy(Strategy):
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        users_targets = context["users"]
        workspace = context["workspace"]
        roles = context["roles"]
        for user_target in users_targets:
            workspace.acl_remove_user(user=user_target)
            workspace.acl_add_user(user=user_target, roles=roles)
        return users_targets, workspace
