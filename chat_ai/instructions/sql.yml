id: generate_sql_code
name: Generate SQL Code
type: generating
description: |
  This YAML configuration defines the rules and standards for generating syntactically valid, schema-compliant,
  and secure SQL queries in response to natural language inputs, optimized for PostgreSQL 14.
  The rules prioritize query accuracy, security, and readability.

rules:
  # Syntax and Structure Rules
  - id: syntactically_valid_sql
    name: Syntactically Valid SQL
    description: SQL Query must be syntactically valid and compatible with PostgreSQL 14.
    severity: high
    required: true

  - id: avoid_ambiguity
    name: Avoid Ambiguity in Columns
    description: SQL Query must avoid ambiguity with columns named "id" and "source_properties" in "layers_record" table.
    severity: high
    required: true

  - id: return_relevant_columns
    name: Return Relevant Columns
    description: SQL Query must only return columns that directly answer the user's question to optimize readability and relevance.
    severity: high
    required: true

  - id: include_WHERE_clause
    name: Include WHERE Clause
    description: SQL Query must include a WHERE clause if the question implies filtering certain records.
    severity: high
    required: true

  - id: add_semicolon
    name: Add Semicolon
    description: Add a semicolon at the end of every SQL Query for completeness.
    severity: medium
    required: true

  - id: follow_good_sql_practices
    name: Follow Good SQL Practices
    description: SQL Query must follow SQL best practices and coding conventions for readability and maintainability.
    severity: medium
    required: true

  # JSON Handling and Data Types
  - id: json_operators_for_json_columns
    name: JSON Operators for JSON Columns
    description: Use appropriate JSON operators (-> or ->>) to extract specific values from JSON columns like "data".
    examples:
      - text: >
          SELECT COUNT()
          FROM "public"."layers_record" AS record
          WHERE record.source_properties->'areas'->>'amana_name_general' = 'جدة'
    severity: high
    required: true

  - id: missing_single_quotes
    name: Missing Single Quotes
    description: Detect missing single quotes around JSON keys, which are required in JSON operator expressions.
    examples:
      - text: >
          SELECT lr.source_properties->>'city' AS city, COUNT(*) AS school_count
          FROM public.layers_record AS lr
          WHERE lr.source_properties->>'poi_type' = 'schools' AND lr.layer_id = 16
          GROUP BY lr.source_properties->>'city';
    pattern:
      - pattern: "'[A-Za-z0-9]+(?:->'[^']')->>[A-Za-z0-9_]+'"
        flags: [case-insensitive]
    severity: high
    required: true

  - id: surround_json_with_type_casts
    name: Surround JSON with Type Casts
    description: Ensure JSON fields are explicitly type-cast as required by PostgreSQL for compatibility, especially in expressions.
    severity: high
    required: true

  - id: json_type_cast_for_similarity_operator
    name: JSON Type Cast for Similarity Operator
    description: Ensure JSON values are cast to `text` type when using similarity operators (e.g., `%` or `similarity()` function) to prevent type mismatch errors.
    severity: high
    required: true

  # Security and Compliance Rules
  - id: avoid_malicious_code
    name: Avoid Malicious Code
    description: SQL Query must not contain any potentially malicious or harmful code.
    severity: critical
    required: true

  - id: valid_sql_query
    name: Valid SQL Query
    description: SQL Query must be valid and free of syntax errors.
    severity: high
    required: true

  - id: respect_enum_values
    name: Respect Enum Values
    description: SQL Query must respect 'enum' values as specified in the JSON schema to ensure accuracy and schema alignment.
    severity: high
    required: true

  # Readability and Code Clarity
  - id: use_descriptive_aliases
    name: Use Descriptive Aliases
    description: SQL Query must use descriptive aliases for columns and tables to improve readability and maintainability.
    severity: medium
    required: true

  - id: extract_sql_code_only
    name: Extract SQL Code Only
    description: Always return only the SQL query code without additional characters or explanation.
    severity: high
    required: true

  # Schema Compliance
  - id: restrict_with_the_database_schema
    name: Restrict With The Database Schema
    description: SQL Query must strictly adhere to the database schema design, using only fields and tables that are defined in the schema considering the enums in the definitions key.
    severity: high
    required: true
