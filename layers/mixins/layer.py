import logging
from typing import List, <PERSON><PERSON>
from uuid import uuid4

from django.conf import settings
from gabbro.graphene import NotFound, BadRequest
from shapely.geometry.base import BaseGeometry
from shapely.geometry.geo import mapping
from shapely.geometry.point import Point

from common.handlers.dataset_files import DatasetFilesLoader
from common.utils import (
    remove_null_values,
    get_valid_geometry,
    calculate_boundaries_postgis,
)
from common.utils import slice_list_to_chunks
from layers.models import Layer, Record, SLD, SLDMapTypes
from layers.models.typed_dict import LayerSLD
from workspaces.enums import LongLatEnum
from workspaces.models import WorkspaceRequest, Workspace

logger = logging.getLogger("layers")


class LayerMixin:
    @staticmethod
    def get_layer_if_exists(layer_id: int) -> Layer:
        layer = (
            Layer.objects.filter(id=layer_id)
            .select_related("dataset", "workspace")
            .first()
        )
        if not layer:
            raise NotFound(
                reason={
                    "layer_id": "Layer with id %(layer_id)s not found"
                    % {"layer_id": layer_id}
                }
            )
        return layer

    def create_geometry_records_from_dataset_file(
        self, layer: Layer, geometry_columns: List[str], summary_fields: List[str]
    ):
        loader = DatasetFilesLoader(file=layer.dataset.file)
        all_features = loader.get_all_features()
        for features in slice_list_to_chunks(
            all_features, chunk_size=settings.RECORDS_BATCH_SIZE
        ):
            self.create_records_from_features(
                features=features,
                layer=layer,
                geometry_columns=geometry_columns,
                summary_fields=summary_fields,
            )

    @staticmethod
    def create_geometry_records_from_layer(layer_source: Layer, layer_target: Layer):
        records_source = layer_source.records.all()
        records_target = []
        for records in slice_list_to_chunks(
            records_source, chunk_size=settings.RECORDS_BATCH_SIZE
        ):
            records_target.extend(
                Record(
                    layer=layer_target,
                    geometry=record.geometry,
                    source_properties=record.source_properties,
                    data=record.data,
                    map_data=record.map_data,
                )
                for record in records
            )
        logger.debug(
            f"[create_geometry_records_from_layer] Creating Records : {len(records_target)} ....."
        )
        Record.objects.bulk_create(
            records_target,
            batch_size=settings.RECORDS_BATCH_SIZE,
            ignore_conflicts=True,
        )
        logger.debug(
            f"[create_geometry_records_from_layer] Created Records : {len(records_target)}"
        )

    def create_records_from_features(
        self,
        features: List[dict],
        layer: Layer,
        geometry_columns: List[str],
        summary_fields: List[str],
    ):
        records = list()
        invalid = list()
        for i, feature in enumerate(features):
            # extract source_properties
            geometry_geojson = self.get_geojson_dict_from_feature(
                feature, geometry_columns
            )
            self.remove_geometry_columns_from_feature_properties(
                feature, geometry_columns
            )
            source_properties = remove_null_values(feature)

            # create geo geometry object
            try:
                geometry = get_valid_geometry(geometry_geojson)
            except Exception as e:
                logger.debug(
                    "create_records_from_features", f"Invalid geometry with error: {e}"
                )
                invalid.append((i, feature))
                continue

            if not geometry.valid:
                invalid.append((i, feature))
                continue

            # create a record object
            r = Record(
                layer=layer,
                geometry=geometry,
                source_properties=source_properties,
                data=source_properties,
                map_data={
                    column: source_properties.get(column) for column in summary_fields
                },
            )
            records.append(r)

        logger.debug(
            f"[create_records_from_features] {layer}: invalid records:{len(invalid)}"
        )
        logger.debug(
            f"[create_records_from_features] {layer}: invalid records:{invalid}"
        )

        # Creating the records in batches
        Record.objects.bulk_create(
            records, batch_size=settings.RECORDS_BATCH_SIZE, ignore_conflicts=True
        )
        logger.debug(f"[create_records_from_features] Created Records : {len(records)}")

    @staticmethod
    def get_geojson_dict_from_feature(feature: dict, geometry_columns: List[str]):
        if len(geometry_columns) == 1:
            geometry = feature[geometry_columns[0]]
        else:
            geometry = Point(feature[geometry_columns[0]], feature[geometry_columns[1]])

        if isinstance(geometry, BaseGeometry):
            return mapping(geometry)
        return geometry

    @staticmethod
    def remove_geometry_columns_from_feature_properties(
        feature: dict, geometry_columns: List[str]
    ):
        for column in geometry_columns:
            feature.pop(column, None)

    @staticmethod
    def get_geometry_columns_from_layer(layer: Layer):
        data = layer.location_field_mapping
        if data["lat_lon_column_num"] == LongLatEnum.column.value:
            return [data["lang_lat_column"]]
        return [data["latitude_column"], data["longitude_column"]]

    @staticmethod
    def update_layer_boundaries(layer: Layer):
        layer.boundaries = calculate_boundaries_postgis(
            records=layer.records.only("geometry")
        )
        layer.save(update_fields=["boundaries", "modified"])
        logger.debug(f"[run_in_background] Layer {layer.key} boundaries calculated")

    @staticmethod
    def validate_if_layer_read_only(layer: Layer):
        if layer.read_only:
            raise BadRequest(
                reason={
                    "layer_id": "Layer with id %(layer_id)s is read only"
                    % {"layer_id": layer.id}
                }
            )

    @classmethod
    def create_layer(
        cls,
        workspace_request: WorkspaceRequest,
        workspace: Workspace,
        summary_fields: list[str],
        columns=None,
    ) -> Tuple[Layer, SLD]:
        layer_data = workspace_request.layer_data
        layer = Layer.objects.create(
            dataset=workspace_request.dataset,
            workspace=workspace,
            key=f"{workspace.pk}_{str(uuid4()).split('-')[0]}",
            data={"summary_fields": summary_fields, "columns": columns},
            title=layer_data["title"],
            description=layer_data.get("description", ""),
            read_only=layer_data.get("read_only", False),
            json_schema=layer_data.get("json_schema"),
            location_field_mapping=layer_data.get("location_field_mapping"),
        )
        sld = cls.create_layer_sld(layer=layer, color=layer_data["color"])
        layer.slds.add(sld)
        return layer, sld

    @staticmethod
    def create_layer_sld(
        layer: Layer, color: str, sld_type: SLDMapTypes = SLDMapTypes.POINTS.value
    ):
        return SLD.objects.create(
            title=layer.key,
            sld_type=sld_type,
            feature_style=LayerSLD(
                color=color,
                opacity=0.5,
                stroke_color=color,
                stroke_width=1,
                stroke_opacity=0.2,
            ),
        )

    def get_geometry_columns(self, layer: Layer):
        if layer.location_field_mapping:
            geometry_columns = self.get_geometry_columns_from_layer(layer=layer)
        else:
            geometry_columns = ["geometry"]
        return geometry_columns
