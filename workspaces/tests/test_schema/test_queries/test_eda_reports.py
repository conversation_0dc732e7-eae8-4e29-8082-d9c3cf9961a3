import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.tests.factories import (
    BaseTestMixin,
    WorkspaceFactory,
    LayerFactory,
    UserFactory,
    DatasetFactory,
)
from workspaces.models import EDAReport, EDAReportSourceChoices

User = get_user_model()


class EDAReportFactory:
    @staticmethod
    def create(dataset, **kwargs):
        """Create an EDA report for testing."""
        defaults = {
            "dataset": dataset,
            "hash_code": "test_hash_code",
            "file": "http://example.com/test_report.html",
            "source": EDAReportSourceChoices.YDATA,
        }
        defaults.update(kwargs)
        return EDAReport.objects.create(**defaults)


class TestEDAReports(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace for testing
        self.workspace = WorkspaceFactory(
            owner=self.user, organization=self.organization
        )
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        )
        # Create a layer for the workspace
        self.layer = LayerFactory(workspace=self.workspace, dataset=self.dataset)

        # Create EDA reports for the layer
        self.eda_reports = [
            EDAReportFactory.create(dataset=self.layer.dataset) for _ in range(5)
        ]

        # GraphQL query for EDA reports
        self.query = """
        query MyQuery($orgId: Int!, $workspaceId: Int!, $layerId: Int!) {
          edaReports(orgId: $orgId, workspaceId: $workspaceId, layerId: $layerId) {
            count
            data {
              id
              file
              source
              created
            }
          }
        }
        """

        # GraphQL query with pk parameter
        self.query_with_pk = """
        query MyQuery($orgId: Int!, $workspaceId: Int!, $layerId: Int!, $pk: Int!) {
          edaReports(orgId: $orgId, workspaceId: $workspaceId, layerId: $layerId, pk: $pk) {
            count
            data {
              id
              file
              source
              created
            }
          }
        }
        """

        self.variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "layerId": self.layer.id,
        }

    def test_query_authorization(self):
        """Test that unauthenticated users cannot access EDA reports."""
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with unauthorized error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        """Test that authenticated users can access EDA reports in their workspace."""
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["edaReports"]

        # Should return all EDA reports for the layer
        self.assertEqual(data["count"], 5)

        # Verify the data of the first EDA report
        report_data = data["data"][0]
        report = EDAReport.objects.filter(id=report_data["id"]).first()

        self.assertEqual(report_data["file"], report.file)
        self.assertEqual(report_data["source"], report.source.upper())

    def test_query_with_superuser(self):
        """Test that superusers can access all EDA reports."""
        # Create a workspace and layer owned by another user
        other_user = UserFactory()
        other_workspace = WorkspaceFactory(
            owner=other_user, organization=self.organization
        )
        dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        )
        other_layer = LayerFactory(workspace=other_workspace, dataset=dataset)
        EDAReportFactory.create(dataset=other_layer.dataset)

        # Execute query as superuser
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["edaReports"]

        # Superuser should see all EDA reports for the specified layer
        self.assertEqual(data["count"], 5)

    def test_query_with_pk(self):
        """Test querying a specific EDA report by ID."""
        # Get the first EDA report
        eda_report = self.eda_reports[0]

        # Set up variables with the EDA report pk
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "layerId": self.layer.id,
            "pk": eda_report.id,
        }

        # Execute the query
        response = self.client.execute(
            self.query_with_pk, variables=variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["edaReports"]

        # Should return exactly one EDA report
        self.assertEqual(data["count"], 1)

        # Verify the returned EDA report is the one we requested
        report_data = data["data"][0]
        self.assertEqual(int(report_data["id"]), eda_report.id)
        self.assertEqual(report_data["file"], eda_report.file)
        self.assertEqual(report_data["source"], eda_report.source.upper())

    def test_query_with_non_existent_pk(self):
        """Test querying a non-existent EDA report."""
        # Get a non-existent EDA report ID
        non_existent_id = 99999

        # Make sure the ID doesn't exist
        self.assertFalse(EDAReport.objects.filter(id=non_existent_id).exists())

        # Set up variables with the non-existent EDA report pk
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "layerId": self.layer.id,
            "pk": non_existent_id,
        }

        # Execute the query
        response = self.client.execute(
            self.query_with_pk, variables=variables, context=self.auth_request
        )

        # Check if the query is successful but returns no EDA reports
        self.assertNotIn("errors", response)
        data = response["data"]["edaReports"]
        self.assertEqual(data["count"], 0)
        self.assertEqual(len(data["data"]), 0)

    def test_query_with_non_existent_workspace(self):
        """Test querying EDA reports with a non-existent workspace ID."""
        # Get a non-existent workspace ID
        non_existent_id = 99999

        # Set up variables with the non-existent workspace ID
        variables = {
            "orgId": self.organization.id,
            "workspaceId": non_existent_id,
            "layerId": self.layer.id,
        }

        # Execute the query
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        # Check if the query returns an error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"workspaceId": _("Invalid workspace_id") % {}},
        )

    def test_query_with_non_existent_layer(self):
        """Test querying EDA reports with a non-existent layer ID."""
        # Get a non-existent layer ID
        non_existent_id = 99999

        # Set up variables with the non-existent layer ID
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "layerId": non_existent_id,
        }

        # Execute the query
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        # Check if the query returns an error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "layerId": _("Layer with id %(layer_id)s not found")
                % {"layer_id": non_existent_id}
            },
        )
