# GeoCore Backend

A comprehensive Django-based geospatial platform that provides advanced GIS capabilities, AI-powered chat functionality, and robust data management for geographic information systems.

## 🌟 Features

- **GraphQL API**: Modern API with comprehensive schema for all operations
- **AI-Powered Chat**: OpenAI integration for intelligent geospatial queries
- **Layer Management**: Advanced geospatial layer handling with GeoServer integration
- **User & Organization Management**: Multi-tenant architecture with role-based access control
- **Workspace Management**: Collaborative workspaces for team-based GIS projects
- **PostGIS Integration**: Advanced spatial database capabilities
- **Real-time Communication**: WebSocket support via Django Channels
- **File Upload & Processing**: Support for various geospatial file formats
- **Internationalization**: Multi-language support (Arabic/English)

## 🛠 Technology Stack

- **Backend**: Django 4.x with Django REST Framework
- **Database**: PostgreSQL with PostGIS extension
- **API**: GraphQL (Graphene-Django)
- **AI**: OpenAI GPT-4 integration
- **GIS**: GeoServer, GeoPandas, Shapely
- **Real-time**: Django Channels with Redis
- **Task Queue**: Celery
- **Containerization**: Docker & Docker Compose
- **Testing**: pytest, coverage
- **Code Quality**: Black, flake8, pre-commit hooks

## 📁 Project Structure

```
geocore-backend/
├── app/                    # Main Django project configuration
│   ├── settings.py        # Django settings
│   ├── urls.py           # URL routing
│   ├── schema.py         # GraphQL schema
│   └── celery.py         # Celery configuration
├── chat_ai/              # AI chat functionality
├── common/               # Shared utilities and models
├── layers/               # Geospatial layer management
├── organizations/        # Organization management
├── proxy/                # Proxy services
├── users/                # User management and authentication
├── workspaces/           # Workspace management
├── docker-compose.yml    # Docker services configuration
├── Dockerfile           # Docker image definition
├── requirements.txt     # Python dependencies
└── manage.py           # Django management script
```

## 🔧 Prerequisites

- Python 3.10+
- Docker & Docker Compose
- PostgreSQL with PostGIS (if running locally)
- Redis (for Celery and Channels)
- Node.js (for frontend development)

## 🚀 Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd geocore-backend
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. **Start services with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Run database migrations**
   ```bash
   docker-compose exec geocore_backend python manage.py migrate
   ```

5. **Create a superuser**
   ```bash
   docker-compose exec geocore_backend python manage.py createsuperuser
   ```

6. **Access the application**
   - API: http://localhost:8000
   - GraphQL Playground: http://localhost:8000/graphql
   - Admin Panel: http://localhost:8000/admin

## ⚙️ Local Development Setup

### 1. Python Environment Setup

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements_dev.txt
```

### 2. Database Setup

```bash
# Install PostgreSQL and PostGIS
# Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib postgis

# Create database
sudo -u postgres createdb geocore_backend
sudo -u postgres psql -c "CREATE USER postgres WITH PASSWORD 'passw0rd';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE geocore_backend TO postgres;"

# Enable PostGIS extension
sudo -u postgres psql geocore_backend -c "CREATE EXTENSION postgis;"
```

### 3. Environment Configuration

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Key environment variables to configure:

```bash
# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
LANGUAGE_CODE=ar

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=geocore_backend
DATABASE_USER=postgres
DATABASE_PASSWORD=passw0rd

# OpenAI Integration
OPENAI_API_KEY=your-openai-api-key

# GeoServer Configuration
GEOSERVER_INTERNAL_URL=http://localhost:8600/geoserver/
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver
WORKSPACE_NAME=geocore
DATA_STORE_NAME=geocore_data

# Storage Configuration
STORAGE_TYPE=LOCAL  # or GCS for Google Cloud Storage
STATIC_URL=/static/
MEDIA_URL=/media/

# Elasticsearch (optional)
ES_HOST=https://localhost
ES_HTTP_AUTH=elastic:passw0rd
```

### 4. Run Migrations and Start Development Server

```bash
# Apply database migrations
python manage.py migrate

# Compile translation messages
python manage.py compilemessages

# Collect static files
python manage.py collectstatic

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

## 🐳 Docker Development

### Services Overview

The Docker Compose setup includes:

- **geocore_backend**: Main Django application
- **postgres**: PostGIS-enabled PostgreSQL database

### Docker Commands

```bash
# Build and start all services
docker-compose up --build

# Start services in background
docker-compose up -d

# View logs
docker-compose logs -f geocore_backend

# Execute commands in container
docker-compose exec geocore_backend python manage.py shell

# Stop all services
docker-compose down

# Remove volumes (⚠️ This will delete all data)
docker-compose down -v
```

### Database Connection

Connect to the PostgreSQL database:

```bash
# Using the Makefile shortcut
make db-connect

# Or manually
PGPASSWORD=passw0rd pgcli -h 127.0.0.1 -p 5433 -U postgres geocore_backend
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
python manage.py test

# Run tests with coverage
make test-coverage

# Run specific app tests
python manage.py test layers

# Run tests in parallel
python manage.py test --parallel=4
```

### Test Configuration

- Tests use pytest framework
- Coverage reports are generated automatically
- Factory Boy is used for test data generation
- Tests run against a separate test database

## 📊 API Documentation

### GraphQL API

The main API endpoint is available at `/graphql` with the following capabilities:

#### Available Queries

- **Users**: User management and authentication
- **Organizations**: Organization and team management
- **Workspaces**: Workspace creation and management
- **Layers**: Geospatial layer operations
- **Messages**: AI chat functionality

#### Available Mutations

- **User Operations**: Registration, authentication, profile updates
- **Layer Operations**: Create, update, delete layers
- **Workspace Operations**: Workspace management
- **Chat AI**: Send messages and get AI responses

#### Example GraphQL Queries

```graphql
# Get user workspaces
query GetWorkspaces($orgId: Int!) {
  workspaces(orgId: $orgId) {
    data {
      id
      name
      description
      created
    }
    count
  }
}

# Send AI chat message
mutation SendChatMessage($input: NewChatInputType!) {
  chatAi(inputForm: $input) {
    success
    message
  }
}
```

### REST API Endpoints

- `/admin/` - Django Admin interface
- `/users/` - User management endpoints
- `/proxy/` - Proxy service endpoints

## 🔧 Development Workflow

### Code Quality

The project uses several tools to maintain code quality:

```bash
# Format code with Black
black .

# Run linting
flake8

# Run pre-commit hooks
pre-commit run --all-files
```

### Database Operations

```bash
# Create new migration
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Reset database (⚠️ Destructive)
python manage.py flush

# Load initial data
python manage.py loaddata fixtures/initial_data.json
```

### Useful Make Commands

```bash
# Clean Python cache files
make clean

# Connect to database
make db-connect

# Run tests with coverage
make test-coverage

# Build and push Docker image
make docker-build-push tag=v1.0.0
```

## 🌍 Internationalization

The project supports multiple languages with Django's i18n framework:

```bash
# Extract translatable strings
python manage.py makemessages -l ar

# Compile translation files
python manage.py compilemessages

# Update existing translations
python manage.py makemessages -a
```

Supported languages:
- Arabic (ar) - Primary language
- English (en) - Secondary language

## 🔐 Security Considerations

### Environment Variables

- Never commit `.env` files to version control
- Use strong, unique passwords for all services
- Rotate API keys regularly
- Use environment-specific configurations

### Database Security

- Enable SSL connections in production
- Use connection pooling
- Regular database backups
- Implement proper access controls

### API Security

- CORS configuration for frontend integration
- Rate limiting on API endpoints
- Input validation and sanitization
- Authentication required for sensitive operations

## 📈 Performance Optimization

### Database Optimization

- Use database indexes for frequently queried fields
- Implement query optimization for large datasets
- Use database connection pooling
- Regular VACUUM and ANALYZE operations

### Caching Strategy

- Redis for session storage and caching
- Database query caching
- Static file caching with CDN
- API response caching where appropriate

## 🚀 Deployment

### Production Checklist

- [ ] Set `DEBUG=False` in production
- [ ] Configure proper database settings
- [ ] Set up SSL certificates
- [ ] Configure static file serving
- [ ] Set up monitoring and logging
- [ ] Configure backup strategies
- [ ] Set up CI/CD pipeline

### Environment-Specific Settings

The project supports multiple deployment environments:

- **LOCAL**: Development environment
- **DEVELOPMENT**: Development server
- **STAGING**: Pre-production testing
- **PRODUCTION**: Live production environment

## 🤝 Contributing

### Development Guidelines

1. **Fork the repository** and create a feature branch
2. **Follow code style** guidelines (Black, flake8)
3. **Write tests** for new functionality
4. **Update documentation** as needed
5. **Submit a pull request** with clear description

### Code Style

- Use Black for code formatting
- Follow PEP 8 guidelines
- Write meaningful commit messages
- Add docstrings to functions and classes
- Keep functions small and focused

### Pull Request Process

1. Ensure all tests pass
2. Update documentation if needed
3. Add appropriate labels
4. Request review from maintainers
5. Address feedback promptly

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help

- **Documentation**: Check this README and inline code documentation
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Discussions**: Use GitHub Discussions for questions and ideas

### Common Issues

#### Database Connection Issues
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql

# Verify PostGIS extension
psql -d geocore_backend -c "SELECT PostGIS_Version();"
```

#### Docker Issues
```bash
# Reset Docker environment
docker-compose down -v
docker-compose up --build

# Check container logs
docker-compose logs geocore_backend
```

#### Migration Issues
```bash
# Reset migrations (⚠️ Development only)
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
find . -path "*/migrations/*.pyc" -delete
python manage.py makemigrations
python manage.py migrate
```

## 📚 Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [PostGIS Documentation](https://postgis.net/documentation/)
- [GraphQL Documentation](https://graphql.org/learn/)
- [Docker Documentation](https://docs.docker.com/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

---

**Made with ❤️ by the GeoCore Team**
