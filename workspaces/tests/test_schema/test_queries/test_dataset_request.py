import os

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequest

User = get_user_model()


class TestWorkspaceRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(
                BASE_DIR, "common", "tests", "fixtures", "sample_geojson.csv"
            )
        )
        self.dataset_requests = WorkspaceRequestFactory.create_batch(
            5,
            dataset=self.dataset,
            created_by=self.user,
            organization=self.organization,
        )
        self.query = """
        query MyQuery($orgId: Int!) {
          workspaceRequests(orgId: $orgId) {
            count
            data {
              status
              layerData
              id
              currentStep
              createdBy {
                email
              }
              dataset {
                metaData
                file
                created
                id
              }
            }
          }
}
        """
        self.variables = {"orgId": self.organization.id}

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaceRequests"]
        self.assertEqual(data["count"], 5)
        data = data["data"][0]
        dataset_request = WorkspaceRequest.objects.filter(id=data["id"]).first()
        self.assertEqual(
            data["status"],
            "IN_PROGRESS",
        )
        self.assertDictEqual(
            data["layerData"],
            dataset_request.layer_data,
        )
        self.assertEqual(
            data["currentStep"],
            dataset_request.current_step,
        )
        created_by = data["createdBy"]
        self.assertEqual(
            created_by["email"],
            dataset_request.created_by.email,
        )
        dataset = data["dataset"]
        self.assertDictEqual(
            dataset["metaData"],
            dataset_request.dataset.meta_data,
        )
        self.assertEqual(
            dataset["file"],
            dataset_request.dataset.file,
        )

    def test_query_with_superuser(self):
        user = UserFactory(is_superuser=True)
        WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=user,
            organization=self.organization,
        )
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaceRequests"]
        self.assertEqual(data["count"], 6)
