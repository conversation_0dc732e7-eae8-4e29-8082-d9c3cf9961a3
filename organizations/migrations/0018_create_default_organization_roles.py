# Generated by Django 3.2.25 on 2025-07-20 08:35
from django.db import migrations

from ._0009_constants import (
    permissions_matrix,
    ADD_WORKSPACE,
    CHANGE_WORKSPACE,
    VIEW_WORKSPACE,
    DELETE_WORKSPACE,
)

rules_objects = list()


def create_organization_roles(apps, schema_editor):
    Permission = apps.get_model("auth", "Permission")
    ContentType = apps.get_model("contenttypes", "ContentType")
    Role = apps.get_model("acl", "Role")
    Organization = apps.get_model("organizations", "Organization")
    organization_content_type = ContentType.objects.get_for_model(Organization)

    for role_title in permissions_matrix.keys():
        role_name = "-".join(role_title.split(" ")).lower()
        role, created = Role.objects.get_or_create(
            codename=role_name, defaults={"title": role_title}
        )
        permissions = []
        for perm in permissions_matrix[role_title]:
            permission, _ = Permission.objects.get_or_create(
                codename=perm, content_type=organization_content_type
            )
            permissions.append(permission)
        role.permissions.set(permissions)
        # cache objects
        rules_objects.append(role)


def migrate_organizations_roles(apps, schema_editor):
    Organization = apps.get_model("organizations", "Organization")
    for org in Organization.objects.all():
        org.roles.add(*rules_objects)


def create_workspace_roles(apps, schema_editor):
    Permission = apps.get_model("auth", "Permission")
    ContentType = apps.get_model("contenttypes", "ContentType")
    Workspace = apps.get_model("workspaces", "Workspace")
    Role = apps.get_model("acl", "Role")
    workspace_permissions_mapper = {
        ADD_WORKSPACE: "workspace-add",
        CHANGE_WORKSPACE: "workspace-change",
        DELETE_WORKSPACE: "workspace-delete",
        VIEW_WORKSPACE: "workspace-view",
    }
    workspace_content_type = ContentType.objects.get_for_model(Workspace)
    for workspace_permission in workspace_permissions_mapper.keys():
        permission, _ = Permission.objects.get_or_create(
            codename=workspace_permission, content_type=workspace_content_type
        )
        role, _ = Role.objects.get_or_create(
            codename=workspace_permissions_mapper[permission.codename],
            defaults={"title": permission.name},
        )
        role.permissions.add(permission)


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0017_alter_organization_options"),
    ]

    operations = [
        migrations.RunPython(create_organization_roles, migrations.RunPython.noop),
        migrations.RunPython(migrate_organizations_roles, migrations.RunPython.noop),
        migrations.RunPython(create_workspace_roles, migrations.RunPython.noop),
    ]
