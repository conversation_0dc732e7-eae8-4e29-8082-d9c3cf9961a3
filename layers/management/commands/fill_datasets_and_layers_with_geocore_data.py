import logging
import time

from django.core.management import BaseCommand
from django.db.models import Q

from layers.management.commands.load_old_tataba_data import (
    fill_datasets_and_layers_with_geocore_data,
)
from workspaces.models import Workspace

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "--workspace_ids",
            type=lambda s: [int(item.strip()) for item in s.split(",")],
            required=False,
            help="Fill Specific Workspaces",
        )
        parser.add_argument(
            "--skip_if_exist",
            nargs="?",
            const=True,
            default=False,
            type=lambda x: str(x).lower() in ("yes", "true", "t", "y", "1"),
            help="Skip existing datasets with EDA reports",
        )

    def handle(self, *args, **options):
        start = time.time()
        logger.debug("Starting ......")
        workspace_ids = options.get("workspace_ids")
        skip_if_exist = options.get("skip_if_exist")
        q_filter = Q()
        if workspace_ids:
            q_filter &= Q(id__in=workspace_ids)
        workspaces = (
            Workspace.objects.filter(q_filter)
            .prefetch_related("layers__dataset")
            .distinct("id")
            .order_by("id")
        )
        logger.debug(f"Updating {len(workspaces)} Workspaces ")
        fill_datasets_and_layers_with_geocore_data(workspaces, skip_if_exist)
        end = time.time()
        logger.debug(f"All Workspaces Updated in {end-start} seconds")
        logger.debug("Exiting ......")
