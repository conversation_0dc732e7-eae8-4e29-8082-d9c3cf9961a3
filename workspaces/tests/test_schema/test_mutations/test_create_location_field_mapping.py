import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequestChoices, RequestTypeChoices
from workspaces.enums import CoordinateTypesEnum, LongLatEnum

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestCreateLocationFieldMapping(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.geojson")
        )
        # Create a workspace request in IN_PROGRESS state
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            request_type=RequestTypeChoices.UPLOAD_FILE,
            layer_data={"title": "Test Dataset", "color": "#e3cec7"},
        )
        self.mutation = """
        mutation MyMutation($datasetRequestId: Int!, $orgId: Int!, $coordinateType: CoordinateTypesEnum!, $latLonColumnNum: LongLatEnum!, $longitudeColumn: String, $latitudeColumn: String, $langLatColumn: String) {
          createLocationFieldMapping(
            dataInput: {datasetRequestId: $datasetRequestId, orgId: $orgId, coordinateType: $coordinateType, latLonColumnNum: $latLonColumnNum, longitudeColumn: $longitudeColumn, latitudeColumn: $latitudeColumn, langLatColumn: $langLatColumn}
          ) {
            datasetRequest {
              id
              status
              requestType
              layerData
              createdBy {
                id
                email
              }
            }
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "orgId": self.organization.id,
            "coordinateType": CoordinateTypesEnum.point.name,
            "latLonColumnNum": LongLatEnum.column.name,
            "langLatColumn": "geometry",
            "longitudeColumn": None,
            "latitudeColumn": None,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot create a location field mapping."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_dataset_request_id(self):
        """Test that an invalid dataset request ID is handled properly."""
        # Use a non-existent dataset request ID
        invalid_variables = self.variables.copy()
        invalid_variables["datasetRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a dataset request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceRequestId": "Dataset with 99999 not found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_cancelled_dataset_request(self):
        """Test that a cancelled dataset request is handled properly."""
        # Cancel the request first
        self.dataset_request.status = WorkspaceRequestChoices.CANCELLED
        self.dataset_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query fails with a dataset request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {self.dataset_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_request_regular_user(self):
        """Test that a regular user cannot update another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            request_type=RequestTypeChoices.UPLOAD_FILE,
            layer_data={"title": "Other User Dataset", "color": "#e3cec7"},
        )

        # Try to update the other user's request
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {other_user_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )
