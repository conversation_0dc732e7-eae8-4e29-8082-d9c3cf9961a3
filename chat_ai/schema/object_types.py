import graphene
from graphene_django import DjangoObjectType
from graphene_gis.scalars import J<PERSON><PERSON><PERSON>ar

from chat_ai.models import Message


class MessageDataType(graphene.ObjectType):
    user_input = graphene.String()
    sql_result = graphene.List(JSONScalar)
    final_result = graphene.String()


class MessageType(DjangoObjectType):
    message = graphene.Field(MessageDataType)

    class Meta:
        model = Message
        fields = ["id", "message"]


class MessageListType(graphene.ObjectType):
    count = graphene.Int()
    data = graphene.List(MessageType)
