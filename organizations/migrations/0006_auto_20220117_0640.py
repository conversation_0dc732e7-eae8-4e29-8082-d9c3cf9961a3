# Generated by Django 3.1.3 on 2022-01-17 06:40

from django.db import migrations


def rename_viewers_to_editors(apps, schema_editor):
    Group = apps.get_model("auth", "Group")
    groups = Group.objects.filter(name__icontains="users")
    for grp in groups:
        grp.name = grp.name.replace("Users", "Editors")
        grp.save()


def reverse_rename(apps, schema_editor):
    Group = apps.get_model("auth", "Group")
    groups = Group.objects.filter(name__icontains="editors")
    for grp in groups:
        grp.name = grp.name.replace("Editors", "Users")
        grp.save()


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0005_auto_20210927_1334"),
    ]

    operations = [
        migrations.RunPython(rename_viewers_to_editors, migrations.RunPython.noop),
    ]
