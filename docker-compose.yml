version: '3.7'
services:
  geocore_backend:
    container_name: geocore_backend
    build: .
    restart: always
    command: >
      sh -c "python manage.py migrate --no-input
             python manage.py collectstatic --no-input
             gunicorn --workers=2 app.wsgi:application --bind 0.0.0.0:8000 --reload"
    volumes:
      - .:/app
      - geocore_static_volume:/app/static
    ports:
      - 8000:8000
    depends_on:
      - postgres

  postgres:
    image: kartoza/postgis
    container_name: geocore_backend_db
    ports:
      - 5433:5432
    volumes:
      - geocore_postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=geocore_backend
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=passw0rd

volumes:
  geocore_postgres_data:
  geocore_static_volume:
