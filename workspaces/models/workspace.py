from django.conf import settings
from django.db import models
from django.db.models.query_utils import Q
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from gabbro.acl.mixins import ResourceAccessControlModelMixin

from organizations.models import Organization
from users.models import User
from workspaces.models import RequestTypeChoices


def get_default_layers_data():
    return dict(layers_count=0, records_count=0)


class WorkspaceManager(models.Manager):
    def permitted_objects(self, user: User, organization: Organization, **kwargs):
        filters = dict(organization=organization)
        if not user.is_superuser:
            filters["owner"] = user
        return super().get_queryset().filter(Q(**kwargs), Q(**filters))


class Workspace(ResourceAccessControlModelMixin, TimeStampedModel):
    owner = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="workspaces",
        verbose_name=_("Owner"),
    )
    organization = models.ForeignKey(
        "organizations.Organization",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="workspaces",
        verbose_name=_("Organization"),
    )
    name = models.CharField(max_length=255, db_index=True, verbose_name=_("Name"))
    description = models.TextField(
        blank=True, default="", db_index=True, verbose_name=_("Description")
    )
    thumbnail = models.URLField(
        null=True, blank=True, max_length=500, verbose_name=_("Map Thumbnail")
    )
    last_visited = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Last Visited")
    )
    layers_sorted_ids = models.JSONField(
        null=True, blank=True, default=list, verbose_name=_("Layers Sorted IDs")
    )
    layers_data = models.JSONField(
        null=True, default=get_default_layers_data, verbose_name=_("Layers Data")
    )
    workspace_type = models.CharField(
        choices=RequestTypeChoices.choices,
        default=RequestTypeChoices.UPLOAD_FILE,
        max_length=20,
        db_index=True,
        verbose_name=_("Workspace Type"),
    )
    objects = WorkspaceManager()

    class Meta:
        verbose_name = _("Workspace")
        verbose_name_plural = _("Workspaces")

    def __str__(self):
        return self.name

    @classmethod
    def create_default_workspace(cls, owner, organization):
        cls.objects.create(
            name="مساحة عمل افتراضية",
            owner=owner,
            organization=organization,
            thumbnail=f"{settings.STATIC_URL}workspaces/default_thumbnail.png",
        )

    def increase_layers_count(self, count: int = 1):
        self.layers_data["layers_count"] += count
        self.save(update_fields=["layers_data", "modified"])

    def increase_records_count(self, count: int = 1):
        self.layers_data["records_count"] += count
        self.save(update_fields=["layers_data", "modified"])

    def decrease_layers_count(self, count: int = 1):
        self.layers_data["layers_count"] -= count
        self.save(update_fields=["layers_data", "modified"])

    def decrease_records_count(self, count: int = 1):
        self.layers_data["records_count"] -= count
        self.save(update_fields=["layers_data", "modified"])
