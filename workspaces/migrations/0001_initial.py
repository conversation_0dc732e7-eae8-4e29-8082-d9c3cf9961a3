# Generated by Django 3.2 on 2024-10-31 08:05

import re

import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Dataset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "file",
                    models.URLField(
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=["csv", "gpkg", "geojson", "kml"]
                            )
                        ],
                        verbose_name="Original Dataset File",
                    ),
                ),
            ],
            options={
                "verbose_name": "Dataset",
                "verbose_name_plural": "Datasets",
            },
        ),
        migrations.CreateModel(
            name="Workspace",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="Name"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        db_index=True,
                        default="",
                        verbose_name="Description",
                    ),
                ),
                (
                    "thumbnail",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="workspaces/thumbnails/",
                        verbose_name="Map Thumbnail",
                    ),
                ),
                (
                    "last_visited",
                    models.DateTimeField(null=True, verbose_name="Last Visited"),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workspaces",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Owner",
                    ),
                ),
            ],
            options={
                "verbose_name": "Workspace",
                "verbose_name_plural": "Workspaces",
            },
        ),
        migrations.CreateModel(
            name="Layer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "key",
                    models.SlugField(
                        max_length=200, unique=True, verbose_name="Unique Form Key"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Layer Title")),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Layer Description"
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#B22222",
                        max_length=7,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Hex Color Invalid!",
                                regex=re.compile("^#([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$"),
                            )
                        ],
                        verbose_name="Fill Color",
                    ),
                ),
                (
                    "read_only",
                    models.BooleanField(
                        default=False,
                        help_text="Is this layer editable or not?",
                        verbose_name="Read Only",
                    ),
                ),
                (
                    "location_field_mapping",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Location Field Mapping"
                    ),
                ),
                (
                    "json_schema",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Form and UI Schemas",
                        verbose_name="JSON Schema",
                    ),
                ),
                (
                    "web_ui_json_schema",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Layer Web UI JsonSchema"
                    ),
                ),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="layers",
                        to="workspaces.dataset",
                        verbose_name="Dataset",
                    ),
                ),
            ],
            options={
                "verbose_name": "Layer",
                "verbose_name_plural": "Layers",
            },
        ),
        migrations.CreateModel(
            name="DatasetRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("in_progress", "In Progress"),
                            ("finished", "Finished"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="in_progress",
                        max_length=20,
                        verbose_name="Request Status",
                    ),
                ),
                (
                    "layer_data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Layer Data"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="requests",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created by",
                    ),
                ),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="requests",
                        to="workspaces.dataset",
                        verbose_name="Dataset",
                    ),
                ),
                (
                    "layer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="requests",
                        to="workspaces.layer",
                        verbose_name="Layer",
                    ),
                ),
            ],
            options={
                "verbose_name": "Dataset Request",
                "verbose_name_plural": "Dataset Requests",
            },
        ),
    ]
