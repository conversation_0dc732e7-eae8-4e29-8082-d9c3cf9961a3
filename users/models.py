from django.db import models
from django.utils.translation import gettext_lazy as _
from gabbro.users.models import User as GabbroUser


class ActiveStatusChoices(models.TextChoices):
    ACTIVE = "active", _("Active")
    INACTIVE = "inactive", _("Inactive")
    WAITING = "waiting", _("Waiting")
    DELETED = "deleted", _("Deleted")


class User(GabbroUser):
    active_status = models.CharField(
        max_length=20,
        choices=ActiveStatusChoices.choices,
        default=ActiveStatusChoices.WAITING,
        verbose_name=_("active status"),
    )

    def __str__(self):
        return f"{self.pk} - {self.email}"
