import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequest
from workspaces.models import WorkspaceRequestChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestCancelWorkspaceRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        )
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        self.file_path = os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        self.mutation = """
        mutation MyMutation($datasetRequestId: Int!, $orgId: Int!) {
          cancelWorkspaceRequest(closeInput: {datasetRequestId: $datasetRequestId, orgId: $orgId}) {
            closed
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_dataset_request(self):
        user = UserFactory(is_superuser=False)
        dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        self.variables = {
            "datasetRequestId": dataset_request.id,
            "orgId": self.organization.id,
        }
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        dataset_request = WorkspaceRequest.objects.get(id=dataset_request.id)
        self.assertEqual("in_progress", dataset_request.status)

    def test_mutation(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        dataset_request = WorkspaceRequest.objects.get(id=self.dataset_request.id)
        self.assertEqual("cancelled", dataset_request.status)
