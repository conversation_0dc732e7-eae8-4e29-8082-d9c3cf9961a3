from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    UserFactory,
)
from workspaces.models import WorkspaceRequest, WorkspaceRequestChoices

User = get_user_model()


class TestWorkspaceRequests(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create test workspace requests
        self.workspace_requests = WorkspaceRequestFactory.create_batch(
            5,
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
        )

        # GraphQL query for workspace requests
        self.query = """
        query MyQuery($orgId: Int!) {
          workspaceRequests(orgId: $orgId) {
            count
            data {
              id
              status
              requestType
              layerData
              currentStep
              createdBy {
                email
              }
              dataset {
                id
                file
                metaData
              }
            }
          }
        }
        """

        # GraphQL query with pk parameter
        self.query_with_pk = """
        query MyQuery($orgId: Int!, $pk: Int!) {
          workspaceRequests(orgId: $orgId, pk: $pk) {
            count
            data {
              id
              status
              requestType
              layerData
              currentStep
              createdBy {
                email
              }
              dataset {
                id
                file
                metaData
              }
            }
          }
        }
        """

        self.variables = {"orgId": self.organization.id}

    def test_query_authorization(self):
        """Test that unauthenticated users cannot access workspace requests."""
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with unauthorized error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        """Test that authenticated users can access their workspace requests."""
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaceRequests"]

        # Should return all workspace requests created by the user
        self.assertEqual(data["count"], 5)

        # Verify the data of the first workspace request
        request_data = data["data"][0]
        workspace_request = WorkspaceRequest.objects.filter(
            id=request_data["id"]
        ).first()

        self.assertEqual(request_data["status"], "IN_PROGRESS")
        self.assertEqual(
            request_data["requestType"], workspace_request.request_type.upper()
        )
        self.assertDictEqual(request_data["layerData"], workspace_request.layer_data)
        self.assertEqual(
            request_data["createdBy"]["email"],
            workspace_request.created_by.email,
        )

    def test_query_with_superuser(self):
        """Test that superusers can access all workspace requests."""
        # Create a workspace request by another user
        other_user = UserFactory()
        WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
        )

        # Execute query as superuser
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaceRequests"]

        # Superuser should see all workspace requests (5 + 1)
        self.assertEqual(data["count"], 6)

    def test_query_with_pk(self):
        """Test querying a specific workspace request by ID."""
        # Get the first workspace request
        workspace_request = self.workspace_requests[0]

        # Set up variables with the workspace request pk
        variables = {"orgId": self.organization.id, "pk": workspace_request.id}

        # Execute the query
        response = self.client.execute(
            self.query_with_pk, variables=variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["workspaceRequests"]

        # Should return exactly one workspace request
        self.assertEqual(data["count"], 1)

        # Verify the returned workspace request is the one we requested
        request_data = data["data"][0]
        self.assertEqual(int(request_data["id"]), workspace_request.id)
        self.assertEqual(request_data["status"], "IN_PROGRESS")
        self.assertEqual(
            request_data["requestType"],
            workspace_request.request_type.value.upper(),
        )
        self.assertDictEqual(request_data["layerData"], workspace_request.layer_data)

    def test_query_with_non_existent_pk(self):
        """Test querying a non-existent workspace request."""
        # Get a non-existent workspace request ID
        non_existent_id = 99999

        # Make sure the ID doesn't exist
        self.assertFalse(WorkspaceRequest.objects.filter(id=non_existent_id).exists())

        # Set up variables with the non-existent workspace request pk
        variables = {"orgId": self.organization.id, "pk": non_existent_id}

        # Execute the query
        response = self.client.execute(
            self.query_with_pk, variables=variables, context=self.auth_request
        )

        # Check if the query is successful but returns no workspace requests
        self.assertNotIn("errors", response)
        data = response["data"]["workspaceRequests"]
        self.assertEqual(data["count"], 0)
        self.assertEqual(len(data["data"]), 0)
