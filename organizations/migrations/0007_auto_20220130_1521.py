# Generated by Django 3.1.3 on 2022-01-30 09:48

from django.core import management
from django.db import migrations
from django.db import transaction


def create_users_groups(apps, schema_editor):
    """Creates and assigns Users role group and permissions on low level (Guardian does not support fake classes)"""
    # Call guardian management command "update_permissions" to create all permissions per model
    management.call_command("update_permissions")

    Group = apps.get_model("auth", "Group")
    Permission = apps.get_model("auth", "Permission")
    GroupObjectPermission = apps.get_model("guardian", "GroupObjectPermission")
    ContentType = apps.get_model("contenttypes", "ContentType")
    Organization = apps.get_model("organizations", "Organization")

    role_name = "Organization Users"
    permissions_code_names = ["view_organization"]
    permissions = [
        Permission.objects.get(codename=codename) for codename in permissions_code_names
    ]
    organization_content_type = ContentType.objects.get(model="organization")
    organizations = Organization.objects.all()

    for org in organizations:
        with transaction.atomic():
            grp, created = Group.objects.get_or_create(name=f"{org.pk}#{role_name}")
            if created:
                for perm in permissions:
                    GroupObjectPermission.objects.create(
                        content_type=organization_content_type,
                        object_pk=org.pk,
                        group=grp,
                        permission=perm,
                    )


class Migration(migrations.Migration):
    dependencies = [
        ("guardian", "0002_generic_permissions_index"),
        ("organizations", "0006_auto_20220117_0640"),
    ]

    operations = [
        migrations.RunPython(create_users_groups, migrations.RunPython.noop),
    ]
