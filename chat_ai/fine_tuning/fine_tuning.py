import logging

from django.conf import settings
from openai import OpenAI

logger = logging.getLogger("chat_ai")

client = OpenAI()


class FineTuning:
    def __init__(self, file_path: str, model_suffix: str):
        self.job_id = None
        self.fine_tuned_model = None
        self.training_file_id = None
        self.gpt_model = settings.FINE_TUNING_MODEL
        self.file_path = file_path
        self.model_suffix = model_suffix

    def upload_and_validate_files(self):
        training_response = client.files.create(
            file=open(self.file_path, "rb"), purpose="fine-tune"
        )
        self.training_file_id = training_response.id
        logger.debug(f"Training file ID: {self.training_file_id}")

    def create_fine_tuning_job(self):
        fine_tuning_job = client.fine_tuning.jobs.create(
            training_file=self.training_file_id,
            model=self.gpt_model,
            suffix=self.model_suffix,
        )
        self.job_id = fine_tuning_job.id
        logger.debug(f"Job ID: {self.job_id}")
        logger.debug(f"Status: {fine_tuning_job.status}")

    def check_job_status(self):
        fine_tuning_job = client.fine_tuning.jobs.retrieve(self.job_id)
        logger.debug(f"Job ID: {fine_tuning_job.id}")
        logger.debug(f"Status: {fine_tuning_job.status}")
        logger.debug(f"Trained Tokens: {fine_tuning_job.trained_tokens}")
        logger.debug(f"Fine Tuned Model {fine_tuning_job.fine_tuned_model}")
        self.fine_tuned_model = fine_tuning_job.fine_tuned_model

    def execute(self):
        self.upload_and_validate_files()
        self.create_fine_tuning_job()
        self.check_job_status()
        return self.fine_tuned_model


"Job ID: ftjob-heyArGOGJMagGVY101waRAMd"
