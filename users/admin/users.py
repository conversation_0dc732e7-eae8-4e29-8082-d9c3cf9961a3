from django.contrib import admin
from django.contrib.auth.models import Group
from gabbro.users.admin import UserAdmin

from users.models import User


class ExtendedUserAdmin(UserAdmin):
    list_display = (
        "id",
        "external_key",
        "email",
        "phone",
        "first_name",
        "last_name",
        "active_status",
    )
    list_filter = ("is_superuser", "is_staff", "active_status")
    search_fields = ["email", "phone", "first_name", "last_name"]

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(Group)
admin.site.register(User, ExtendedUserAdmin)
