from unittest.mock import patch, MagicMock

from django.conf import settings
from django.test import TestCase
from django.utils.translation import gettext_lazy as _

from chat_ai.schema.input_object_types import QuestionTypeEnum
from chat_ai.schema.mutation import GPT_MODEL_MAPPER
from common.tests.factories import (
    UserFactory,
    BaseTestMixin,
    WorkspaceFactory,
    LayerFactory,
    ConversationFactory,
)


class TestChatAI(BaseTestMixin):
    def setUp(self):
        super().setUp()

        self.workspace = WorkspaceFactory(
            name="Test Workspace", organization=self.organization
        )
        self.layer = LayerFactory()
        self.workspace.layers.add(self.layer)
        # Create the mutation string
        self.mutation = """
        mutation SendMessage($input: NewChatInputType!) {
          sendMessageChatAi(inputForm: $input) {
            response
            dbResult
          }
        }
        """

        # Create the variables for the mutation
        self.variables = {
            "input": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "question": "What is the data about?",
                "layersIds": [self.layer.id],
                "questionType": "DATA",
            }
        }


class TestValidation(TestChatAI):
    def test_query_authorization(self):
        """Test that unauthorized users cannot execute the mutation."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an unauthorized error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    @patch("chat_ai.schema.mutation.num_tokens_from_string")
    def test_token_limit_validation(self, mock_num_tokens):
        """Test that questions exceeding the token limit are rejected."""
        # Mock the token count to exceed the limit
        mock_num_tokens.return_value = settings.USER_INPUT_TOKENS + 1

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query fails with a token limit error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"question": "your question is too long, try to short it"},
        )

    def test_workspace_validation(self):
        """Test that invalid workspace IDs are rejected."""
        # Use an invalid workspace ID
        self.variables["input"]["workspaceId"] = 9999

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query fails with a workspace validation error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"workspaceId": _("Invalid workspace_id") % {}},
        )

    def test_layers_validation(self):
        """Test that invalid layer IDs are rejected."""
        # Use an invalid layer ID
        self.variables["input"]["layersIds"] = [9999]

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query fails with layers validation error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"layersIds": _("No layers found with the provided IDs") % {}},
        )


class TestPermission(TestChatAI):
    def test_organization_permission(self):
        """Test that users without organization permissions cannot execute the mutation."""
        # Create a user without organization permissions
        user_without_perms = UserFactory(is_superuser=False)

        # Create a request context with the user without permissions
        request = MagicMock()
        request.user = user_without_perms
        request.organization = self.organization

        response = self.client.execute(
            self.mutation, variables=self.variables, context=request
        )

        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertIn("Unauthorized", response["errors"][0]["message"])


class TestExecution(TestChatAI):
    @patch("chat_ai.schema.mutation.ChatGPT")
    @patch("chat_ai.schema.mutation.num_tokens_from_string")
    def test_successful_execution(self, num_tokens_from_string_mock, mock_chat_gpt):
        """Test successful execution of the mutation."""
        # Mock the ChatGPT instance and its execute method
        mock_instance = MagicMock()
        mock_chat_gpt.return_value = mock_instance
        mock_instance.execute.return_value = ("This is a response", {"key": "value"})
        num_tokens_from_string_mock.return_value = settings.USER_INPUT_TOKENS - 1
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["sendMessageChatAi"]
        self.assertEqual(data["response"], "This is a response")
        self.assertEqual(data["dbResult"], {"key": "value"})

        # Verify that ChatGPT was initialized with the correct parameters
        mock_chat_gpt.assert_called_once()
        call_args = mock_chat_gpt.call_args[1]
        self.assertEqual(call_args["user"], self.user)
        self.assertEqual(call_args["user_input"], self.variables["input"]["question"])
        self.assertEqual(
            call_args["gpt_model"], GPT_MODEL_MAPPER[QuestionTypeEnum.DATA.value]
        )

    @patch("chat_ai.schema.mutation.ChatGPT")
    @patch("chat_ai.schema.mutation.num_tokens_from_string")
    def test_conversation_retrieval(self, num_tokens_from_string_mock, mock_chat_gpt):
        """Test that the mutation retrieves the correct conversation."""
        num_tokens_from_string_mock.return_value = settings.USER_INPUT_TOKENS - 1
        # Create a conversation with the layer
        conversation = ConversationFactory(user=self.user)
        conversation.layers.add(self.layer)

        # Mock the ChatGPT instance and its execute method
        mock_instance = MagicMock()
        mock_chat_gpt.return_value = mock_instance
        mock_instance.execute.return_value = ("This is a response", {"key": "value"})

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify that ChatGPT was initialized with the correct conversation ID
        mock_chat_gpt.assert_called_once()
        call_args = mock_chat_gpt.call_args[1]
        self.assertEqual(call_args["conversation_id"], conversation.id)


class TestGPTModelMapper(TestCase):
    """Test the GPT_MODEL_MAPPER dictionary."""

    def test_gpt_model_mapper(self):
        """Test that the GPT_MODEL_MAPPER maps question types to the correct models."""
        self.assertEqual(
            GPT_MODEL_MAPPER[QuestionTypeEnum.DATA.value], settings.POSTGRES_MODEL
        )
        self.assertEqual(
            GPT_MODEL_MAPPER[QuestionTypeEnum.GIS.value], settings.GIS_MODEL
        )
        self.assertEqual(
            GPT_MODEL_MAPPER[QuestionTypeEnum.DATA_ACTION.value], settings.DJANGO_MODEL
        )
        self.assertEqual(
            GPT_MODEL_MAPPER[QuestionTypeEnum.GENERAL.value], settings.DJANGO_MODEL
        )
