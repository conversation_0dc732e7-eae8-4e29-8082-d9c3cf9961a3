from django.core.exceptions import ValidationError
from django.db import transaction
from gabbro.acl.models import Role
from gabbro.graphene import BadRequest

from common.clients.accounts_client import accounts
from common.interfaces import Strategy
from organizations.models import Organization
from users.models import User, ActiveStatusChoices
from workspaces.models import Workspace


class AddUserStrategy(Strategy):
    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        role: Role = context["role"]
        user_data = get_user_data_from_accounts(
            email=data_input["email"], phone=data_input["phone"]
        )
        user_added = User.objects.filter(external_key=user_data.get("pk")).first()
        if user_added:
            user_added.email = user_data.get("email")
            user_added.phone = user_data.get("phone")
            user_added.first_name = data_input.get("first_name")
            user_added.last_name = data_input.get("last_name")
            user_added.active_status = ActiveStatusChoices.ACTIVE
            user_added.save()
        else:
            user_added = User.objects.create_user(
                external_key=user_data.get("pk"),
                email=user_data.get("email"),
                phone=user_data.get("phone"),
                first_name=data_input.get("first_name"),
                last_name=data_input.get("last_name"),
            )
        organization.acl_remove_user(user=user_added)
        organization.acl_add_user(user=user_added, roles=[role])
        return user_added


class ChangeUserActiveStatusStrategy(Strategy):
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        user_target = context["user_target"]
        user_target.active_status = data_input["active_status"]
        user_target.save(update_fields=["active_status"])
        if user_target.active_status == ActiveStatusChoices.DELETED:
            organization.acl_remove_user(user=user_target)
            workspaces = Workspace.acl_objects_for_user(user=user, perms=[]).distinct()
            for workspace in workspaces:
                workspace.acl_remove_user(user=user_target)
        return user_target


def get_user_data_from_accounts(email: str, phone: str):
    # Try to retrieve the user by email first
    user_data, _ = accounts.internal_retrieve_user(data={"email": email})

    # If not found by email, try the phone number
    if not user_data:
        user_data, _ = accounts.internal_retrieve_user(data={"phone": phone})

    # If the user doesn't exist, create a new user
    if not user_data:
        try:
            user_data = accounts.internal_create_user(
                data={"email": email, "phone": phone}
            )
        except ValidationError as e:
            raise BadRequest(reason=e.message_dict)

    return user_data
