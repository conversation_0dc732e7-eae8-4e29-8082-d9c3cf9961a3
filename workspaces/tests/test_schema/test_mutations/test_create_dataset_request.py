import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test import TransactionTestCase
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
)
from common.utils import ALLOWED_DATASET_EXTENSIONS
from workspaces.models import WorkspaceRequestChoices

User = get_user_model()


class TestCreateWorkspaceRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.file_path = os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        self.mutation = """
            mutation MyMutation($color: String!, $datasetFile: String!, $description: String!, $readOnly: Boolean!, $title: String!, $orgId: Int!) {
              createDataset(
                dataInput: {title: $title, color: $color, datasetFile: $datasetFile, description: $description, readOnly: $readOnly, orgId: $orgId}
              ) {
                datasetRequest {
                  currentStep
                  id
                  layerData
                  status
                  createdBy {
                    email
                  }
                }
              }
            }
        """
        self.variables = {
            "color": "#42FAC2",
            "datasetFile": self.file_path,
            "description": "",
            "readOnly": False,
            "title": "test dataset",
            "orgId": self.organization.id,
        }


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestValidation(TestCreateWorkspaceRequest):
    def test_query_authorization(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_validate_in_progress_dataset_request_func(self):
        dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        )
        WorkspaceRequestFactory(
            dataset=dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"datasetFile": _("You have Upload File request in progress.") % {}},
        )

    def test_validate_dataset_file_unc(self):
        self.variables["datasetFile"] = "test.xyz"
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "datasetFile": _("Dataset file extension must be on of %(extension)s")
                % {"extension": ", ".join(ALLOWED_DATASET_EXTENSIONS)}
            },
        )


class TestStrategy(TestCreateWorkspaceRequest, TransactionTestCase):
    def test_validate_and_create_dataset_layer_data_func(self):
        self.variables["color"] = "42FAC2"
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], "Bad Request")
        self.assertIn(
            "layerData",
            response["errors"][0]["extensions"]["http"]["reason"],
        )
