# Generated by Django 3.2.25 on 2025-04-26 16:47

from django.db import migrations


def migrate_conversation_layers(apps, schema_editor):
    Conversation = apps.get_model("chat_ai", "Conversation")
    conversations = Conversation.objects.all()
    for conversation in conversations:
        conversation.layers.add(conversation.layer)


class Migration(migrations.Migration):

    dependencies = [
        ("chat_ai", "0007_auto_20250426_1646"),
    ]

    operations = [
        migrations.RunPython(migrate_conversation_layers, migrations.RunPython.noop),
    ]
