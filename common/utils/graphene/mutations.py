import datetime

from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from google.cloud import storage

STORAGE_TYPE = getattr(settings, "STORAGE_TYPE", None)
GS_CREDENTIALS = getattr(settings, "GS_CREDENTIALS", None)
GS_MEDIA_BUCKET_NAME = getattr(settings, "GS_MEDIA_BUCKET_NAME", None)
ORIGIN = getattr(settings, "PLATFORM_WEB_URL", "").split(",") or ["*"]
if all([STORAGE_TYPE == "GS", GS_MEDIA_BUCKET_NAME is None, GS_CREDENTIALS is None]):
    raise ImproperlyConfigured("GCS signed urls wrong configurations")


def generate_upload_signed_url_v4(
    blob_name, bucket_name=GS_MEDIA_BUCKET_NAME, expiration: int = 15
):
    """Generates a v4 signed URL for uploading a blob using HTTP PUT."""
    storage_client = storage.Client(credentials=GS_CREDENTIALS)
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    return blob.generate_signed_url(
        version="v4",
        expiration=datetime.timedelta(minutes=expiration),
        method="PUT",
        content_type="application/octet-stream",
    )
