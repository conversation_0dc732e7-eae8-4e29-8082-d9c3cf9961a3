from django.db.models.query_utils import Q
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from organizations.models import Organization
from workspaces.models import Workspace, WorkspaceRequest


class WorkspaceMixin:
    @staticmethod
    def validate_and_get_workspace(workspace_id: int, org_id: int = None) -> Workspace:
        org_query = Q(Q(organization_id=org_id) if org_id else Q())
        workspace = Workspace.objects.filter(org_query, id=workspace_id).first()
        if not workspace:
            raise NotFound(
                reason={
                    "workspace_id": _("Workspace with %(workspace_id)s not found")
                    % {"workspace_id": workspace_id},
                }
            )
        return workspace

    @staticmethod
    def create_workspace(
        workspace_request: WorkspaceRequest, organization: Organization
    ) -> Workspace:
        name = workspace_request.layer_data["title"]
        description = workspace_request.layer_data.get("description", "")
        return Workspace.objects.create(
            owner=workspace_request.created_by,
            name=name,
            description=description,
            organization=organization,
            workspace_type=workspace_request.request_type,
        )
