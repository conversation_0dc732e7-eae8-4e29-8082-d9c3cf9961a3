# Django and deploymenet specific
SECRET_KEY=123test
DEBUG=True
LANGUAGE_CODE=ar
PROJECT_NAME=geocore
# internal network mode True/False values
INTERNAL_MODE=True

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=geocore_backend
DATABASE_USER=postgres
DATABASE_PASSWORD=passw0rd

# SKARN
BASE_URL=http://127.0.0.1:8000

SKARN_REGISTRATION_ENABLED=False

SKARN_HYDRA_ADMIN_URL=http://127.0.0.1:7001
SKARN_HYDRA_PUBLIC_URL=http://127.0.0.1:7000
SKARN_HYDRA_SSL_VERIFY=False

# Accounts app
ACCOUNTS_BASE_URL=http://127.0.0.1:8001
ACCOUNTS_INTERNAL_BASE_URL=http://127.0.0.1:8001
ACCOUNTS_USERNAME=<EMAIL>
ACCOUNTS_PASSWORD=admin

# Storage
STORAGE_TYPE=LOCAL
GC_PROJECT_ID=
GC_CREDENTIALS_INFO_PRIVATE_KEY_ID=
GC_CREDENTIALS_INFO_PRIVATE_KEY=
GC_CREDENTIALS_INFO_CLIENT_USERNAME=
GC_CREDENTIALS_INFO_CLIENT_ID=
GS_STATIC_BUCKET_NAME=
GS_MEDIA_BUCKET_NAME=
STATIC_URL=/static/
MEDIA_URL=/media/

# Elasticsearch
ES_HOST=https://localhost
ES_PORT=
ES_HTTP_AUTH=elastic:passw0rd

# Google
GOOGLE_API_KEY=

# Layers Django App
# -- Elasticsearch --
RECORDS_THRESHOLD_CLUSTERS=10000
CLUSTERS_CLASSIFICATION_NUMBER_OF_CLASSES=11
CLUSTER_RADIUS_OFFSET=1
PRECISION_OFFSET=2
# layers' index name prefix
LAYERS_INDEX_NAME_PREFIX=geocore

# -- GeoServer --
# Local
GEOSERVER_INTERNAL_URL=http://localhost:8600/geoserver/
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver
WORKSPACE_NAME=geocore
DATA_STORE_NAME=geocore_data
# DEV
#GEOSERVER_INTERNAL_URL="https://geoserver.dev.geotech.run/geoserver/"
#GEOSERVER_USERNAME="admin"
#GEOSERVER_PASSWORD="Wn4h8mM7YnuT9m"

# -- Sentry --
SENTRY_DSN=
# LOCAL / DEVELOPMENT / STAGIGN / PRODUCTION
DEPLOYMENT=LOCAL

# -- Makan Client --
MAKAN_BASE_URL=https://api.makan.run/api/
MAKAN_TOKEN=X2k9DFSe6EgeitZVqVWFzkslCJ2dfYH8kcU8qNA5PG2dH8U3mIczP1lsV5GwvLsL
MAKAN_MAPER_VERSION=v1

DATASET_SAMPLE_ROWS_NUMBER=5
RECORDS_BATCH_SIZE=1000

OPENAI_API_KEY=test
