# Generated by Django 3.2.25 on 2024-12-26 08:21

from django.db import migrations


def update_workspaces_with_layers_sorted_ids(apps, schema_editor):
    Workspace = apps.get_model("workspaces", "Workspace")
    workspaces = Workspace.objects.filter(layers_sorted_ids__isnull=True)
    for workspace in workspaces:
        workspace.layers_sorted_ids = list(
            workspace.layers.order_by("-created").values_list("id", flat=True)
        )
    Workspace.objects.bulk_update(workspaces, ["layers_sorted_ids", "modified"])


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0007_workspace_layers_sorted_ids"),
    ]

    operations = [
        migrations.RunPython(
            update_workspaces_with_layers_sorted_ids, migrations.RunPython.noop
        )
    ]
