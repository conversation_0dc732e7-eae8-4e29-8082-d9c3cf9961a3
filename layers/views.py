from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from layers.models import Layer, LayerStatusChoices
from layers.scripts import PublishLayerToGeoserver


@login_required
def publish_layer_to_geoserver(request, pk):
    redirect_url = redirect(reverse("admin:layers_layer_changelist"))

    if not request.user.is_superuser:
        messages.error(
            request=request,
            message=_("Your are not authorized to view this page.") % {},
        )
        return redirect_url

    layer = Layer.objects.filter(pk=pk, status=LayerStatusChoices.UNPUBLISHED).first()
    if not layer:
        messages.warning(
            request=request,
            message=_(
                "Can't publish layer %(pk)s because the layer is already published."
            )
            % {"pk": pk}
            % {},
        )
        return redirect_url

    publisher = PublishLayerToGeoserver(layer=layer)
    publisher.publish()
    messages.success(
        message=_("Layer %(layer)s is published") % {"layer": layer}, request=request
    )
    return redirect_url
