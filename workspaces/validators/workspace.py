from django.db.models.query_utils import Q
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.interfaces import InputValidation
from organizations.models import Organization
from users.models import User
from workspaces.mixins import WorkspaceMixin
from workspaces.mixins.dataset import DatasetMixin
from workspaces.models import WorkspaceRequest, WorkspaceRequestChoices, Workspace
from workspaces.serializers import (
    LayerDataSerializer,
    LocationFieldMappingSerializer,
    JSONSchemaSerializer,
    WorkspaceSerializer,
)


class UpdateWorkspaceValidator(InputValidation, WorkspaceMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        workspace_id = data_input["workspace_id"]
        workspace = self.validate_and_get_workspace(workspace_id, data_input["org_id"])
        if layers_sorted_ids := data_input.get("layers_sorted_ids"):
            self.validate_layers_sorted_ids_is_valid_layers(
                workspace=workspace, layers_sorted_ids=layers_sorted_ids
            )
        serializer = WorkspaceSerializer(
            instance=workspace,
            data=data_input,
            partial=True,
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return {"workspace": workspace}

    @staticmethod
    def validate_layers_sorted_ids_is_valid_layers(
        workspace: Workspace, layers_sorted_ids: list
    ):
        layers = workspace.layers.filter(id__in=layers_sorted_ids).values_list(
            "id", flat=True
        )
        if set(layers_sorted_ids) != set(layers):
            raise BadRequest(
                reason={
                    "layers_sorted_ids": _(
                        "Layers sorted ids must be a subset of workspace layers"
                    )
                    % {}
                }
            )


class CreateWorkspaceValidator(InputValidation, DatasetMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        dataset_request_id = data_input.get("dataset_request_id")
        dataset_request = self.validate_in_progress_dataset_request(
            dataset_request_id=dataset_request_id,
            org_id=data_input.get("org_id"),
            user=user,
        )
        if map_data_columns := data_input.get("map_data_columns"):
            self.validate_if_map_data_columns_included_in_dataset_sample(
                dataset=dataset_request.dataset,
                map_data_columns=map_data_columns,
            )
        self.validate_dataset_request_data(dataset_request=dataset_request)
        return {"dataset_request": dataset_request}

    @staticmethod
    def validate_in_progress_dataset_request(
        dataset_request_id: int, org_id: int, user: User
    ) -> WorkspaceRequest:
        org_query = Q(
            Q(organization_id=org_id)
            if user.is_superuser
            else Q(organization_id=org_id) & Q(created_by=user),
        )
        dataset_request = (
            WorkspaceRequest.objects.filter(
                org_query,
                id=dataset_request_id,
                status=WorkspaceRequestChoices.IN_PROGRESS,
                layer_data__isnull=False,
            )
            .select_related("dataset")
            .first()
        )
        if not dataset_request:
            raise BadRequest(
                reason={
                    "dataset_request_id": _(
                        "Dataset with %(dataset_request_id)s not found"
                    )
                    % {"dataset_request_id": dataset_request_id}
                }
            )
        return dataset_request

    @staticmethod
    def validate_dataset_request_data(dataset_request: WorkspaceRequest):
        layer_data = dataset_request.layer_data or dict()
        location_field_mapping = layer_data.get("location_field_mapping")
        json_schema = layer_data.get("json_schema")
        web_ui_json_schema = layer_data.get("web_ui_json_schema")
        data_layer_serializer = LayerDataSerializer(data=layer_data)
        if not data_layer_serializer.is_valid():
            raise BadRequest(reason=data_layer_serializer.errors)

        if location_field_mapping:
            location_field_mapping_serializer = LocationFieldMappingSerializer(
                data=location_field_mapping
            )
            if not location_field_mapping_serializer.is_valid():
                raise BadRequest(reason=location_field_mapping_serializer.errors)

        if json_schema:
            json_schema_serializer = JSONSchemaSerializer(
                data=dict(
                    json_schema=json_schema, web_ui_json_schema=web_ui_json_schema
                )
            )
            if not json_schema_serializer.is_valid():
                raise BadRequest(reason=json_schema_serializer.errors)


class DeleteWorkspaceValidator(InputValidation, WorkspaceMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        workspace_id = data_input["workspace_id"]
        workspace = self.validate_and_get_workspace(
            workspace_id=workspace_id, org_id=data_input["org_id"]
        )
        return {"workspace": workspace}


class CreateEmptyWorkspaceValidator(InputValidation):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        return dict()
