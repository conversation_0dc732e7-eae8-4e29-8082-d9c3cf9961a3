import graphene
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from gabbro.acl.models import Role
from graphene_django import DjangoObjectType

User = get_user_model()


class PermissionType(DjangoObjectType):
    class Meta:
        model = Permission
        fields = ("id", "name", "codename")


class PermissionModelType(graphene.ObjectType):
    model = graphene.String()
    permissions = graphene.List(PermissionType)


class RoleType(DjangoObjectType):
    users_count = graphene.Int()
    permissions = graphene.List(PermissionType)

    class Meta:
        model = Role
        fields = ("id", "title", "codename", "permissions")

    def resolve_users_count(self: Role, info):
        if organization := getattr(info.context, "organization", None):
            return organization.acl_users_per_role(self).count()
        return 0

    def resolve_permissions(self: Role, info):
        return self.permissions.all()


class RoleListType(graphene.ObjectType):
    data = graphene.List(RoleType)
    count = graphene.Int()


class WorkspacePermissionType(DjangoObjectType):
    class Meta:
        model = Role
        fields = ("id", "title", "codename")


class UserType(DjangoObjectType):
    id = graphene.Int()
    role = graphene.Field(RoleType)
    workspace_permissions = graphene.List(WorkspacePermissionType)

    class Meta:
        model = User
        fields = (
            "id",
            "first_name",
            "last_name",
            "email",
            "phone",
            "is_superuser",
            "is_staff",
            "active_status",
            "avatar",
            "role",
            "workspace_permissions",
        )

    def resolve_role(self: User, info):
        if organization := getattr(info.context, "organization", None):
            return organization.acl_user_roles(self).first()
        return None

    def resolve_workspace_permissions(self: User, info):
        if workspace := getattr(info.context, "workspace", None):
            return workspace.acl_user_roles(self)
        return None


class UserListType(graphene.ObjectType):
    data = graphene.List(UserType)
    count = graphene.Int()
