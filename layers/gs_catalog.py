from django.conf import settings
from gabbro.layers_engine.geoserver.catalog import (
    GeoServerCatalog,
)

GEOSERVER_SETTINGS = getattr(settings, "GEOSERVER_SETTINGS", dict())

layer_attributes = {
    "attribute": [
        {
            "name": "id",
            "minOccurs": 1,
            "maxOccurs": 1,
            "nillable": False,
            "binding": "java.lang.Long",
        },
        {
            "name": "geometry",
            "minOccurs": 0,
            "maxOccurs": 1,
            "nillable": True,
            "binding": "org.locationtech.jts.geom.Geometry",
        },
        {
            "name": "buffer_geometry",
            "minOccurs": 0,
            "maxOccurs": 1,
            "nillable": True,
            "binding": "org.locationtech.jts.geom.Geometry",
        },
        {
            "name": "map_data",
            "minOccurs": 1,
            "maxOccurs": 1,
            "nillable": False,
            "binding": "java.lang.String",
        },
        {
            "name": "layer_id",
            "minOccurs": 0,
            "maxOccurs": 1,
            "nillable": True,
            "binding": "java.lang.Long",
        },
    ]
}

catalog = GeoServerCatalog(geoserver_settings=GEOSERVER_SETTINGS)
