from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from common.tests.factories import (
    BaseTestMixin,
    WorkspaceFactory,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import Dataset, Workspace

User = get_user_model()


class TestDatasets(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace for testing
        self.workspace = WorkspaceFactory(
            owner=self.user, organization=self.organization
        )

        # Create datasets for the workspace
        self.datasets = DatasetFactory.create_batch(5, workspace=self.workspace)

        # GraphQL query for datasets
        self.query = """
        query MyQuery($orgId: Int!, $workspaceId: Int!) {
          datasets(orgId: $orgId, workspaceId: $workspaceId) {
            count
            data {
              id
              title
              file
              metaData
              created
            }
          }
        }
        """

        # GraphQL query with pk parameter
        self.query_with_pk = """
        query MyQuery($orgId: Int!, $workspaceId: Int!, $pk: Int!) {
          datasets(orgId: $orgId, workspaceId: $workspaceId, pk: $pk) {
            count
            data {
              id
              title
              file
              metaData
              created
            }
          }
        }
        """

        self.variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
        }

    def test_query_authorization(self):
        """Test that unauthenticated users cannot access datasets."""
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with unauthorized error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        """Test that authenticated users can access datasets in their workspace."""
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["datasets"]

        # Should return all datasets in the workspace
        self.assertEqual(data["count"], 5)

        # Verify the data of the first dataset
        dataset_data = data["data"][0]
        dataset = Dataset.objects.filter(id=dataset_data["id"]).first()

        self.assertEqual(dataset_data["title"], dataset.title)
        self.assertEqual(dataset_data["file"], dataset.file)
        self.assertEqual(dataset_data["metaData"], dataset.meta_data)

    def test_query_with_superuser(self):
        """Test that superusers can access all datasets."""
        # Create a workspace and datasets owned by another user
        other_user = UserFactory()
        other_workspace = WorkspaceFactory(
            owner=other_user, organization=self.organization
        )
        DatasetFactory.create_batch(3, workspace=other_workspace)

        # Execute query as superuser
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["datasets"]

        # Superuser should see all datasets in the specified workspace
        self.assertEqual(data["count"], 5)

    def test_query_with_pk(self):
        """Test querying a specific dataset by ID."""
        # Get the first dataset
        dataset = self.datasets[0]

        # Set up variables with the dataset pk
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "pk": dataset.id,
        }

        # Execute the query
        response = self.client.execute(
            self.query_with_pk, variables=variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["datasets"]

        # Should return exactly one dataset
        self.assertEqual(data["count"], 1)

        # Verify the returned dataset is the one we requested
        dataset_data = data["data"][0]
        self.assertEqual(int(dataset_data["id"]), dataset.id)
        self.assertEqual(dataset_data["title"], dataset.title)
        self.assertEqual(dataset_data["file"], dataset.file)
        self.assertEqual(dataset_data["metaData"], dataset.meta_data)

    def test_query_with_non_existent_pk(self):
        """Test querying a non-existent dataset."""
        # Get a non-existent dataset ID
        non_existent_id = 99999

        # Make sure the ID doesn't exist
        self.assertFalse(Dataset.objects.filter(id=non_existent_id).exists())

        # Set up variables with the non-existent dataset pk
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "pk": non_existent_id,
        }

        # Execute the query
        response = self.client.execute(
            self.query_with_pk, variables=variables, context=self.auth_request
        )

        # Check if the query is successful but returns no datasets
        self.assertNotIn("errors", response)
        data = response["data"]["datasets"]
        self.assertEqual(data["count"], 0)
        self.assertEqual(len(data["data"]), 0)

    def test_query_with_non_existent_workspace(self):
        """Test querying datasets with a non-existent workspace ID."""
        # Get a non-existent workspace ID
        non_existent_id = 99999

        # Make sure the ID doesn't exist
        self.assertFalse(Workspace.objects.filter(id=non_existent_id).exists())

        # Set up variables with the non-existent workspace ID
        variables = {"orgId": self.organization.id, "workspaceId": non_existent_id}

        # Execute the query
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        # Check if the query returns an error
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "workspaceId": _("Workspace with id %(workspace_id)s not found")
                % {"workspace_id": non_existent_id}
            },
        )
