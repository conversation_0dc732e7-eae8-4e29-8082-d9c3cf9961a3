from common.interfaces import PermissionsInterface
from common.utils import authorize_user
from organizations.perms_constants import CHANGE_ROLE


class ChangeUserRolePerms(PermissionsInterface):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=CHANGE_ROLE)


class AssignWorkspaceUserPerms(PermissionsInterface):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=CHANGE_ROLE)
