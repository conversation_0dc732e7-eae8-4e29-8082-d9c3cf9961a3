# Generated by Django 3.2.25 on 2025-04-24 20:20

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0017_layer_records_last_modified"),
        ("workspaces", "0017_workspace_workspace_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="EDAReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "hash_code",
                    models.CharField(max_length=500, verbose_name="hash code"),
                ),
                ("file", models.URLField(verbose_name="file")),
                (
                    "layer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="eda_reports",
                        to="layers.layer",
                        verbose_name="layer",
                    ),
                ),
            ],
            options={
                "verbose_name": "EDA Report",
                "verbose_name_plural": "EDA Reports",
            },
        ),
    ]
