import json
import logging
import re
from typing import <PERSON><PERSON>
from uuid import UUID

from django.conf import settings
from django.db import connection
from django.db.models import QuerySet
from openai import OpenAI
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from tiktoken import encoding_for_model

from chat_ai.ai_moduls.prompts import (
    get_sql_prompt,
    FORMAT_PROMPT_GENERAL_QUESTION,
    FORMAT_PROMPT,
    FORMAT_PROMPT_LARGE_DATA,
)
from chat_ai.models import Conversation, Message
from chat_ai.models.utils import MessageData
from layers.models import Layer
from users.models import User

client = OpenAI()
logger = logging.getLogger("chat_ai")


class ChatGPT:
    def __init__(
        self,
        layers: QuerySet[Layer],
        user: User,
        gpt_model: str,
        user_input: str,
        conversation_id: UUID = None,
    ):
        self.user = user
        self.layers = layers
        self.layer_ids = list(self.layers.values_list("id", flat=True))
        self.gpt_model = gpt_model
        self.user_input = user_input

        # Create or get the conversation
        if conversation_id:
            self.conversation = Conversation.objects.get(id=conversation_id)
        else:
            self.conversation = Conversation.objects.create(user=self.user)

        # Add layers to the conversation if they don't exist
        existing_layer_ids = set(self.conversation.layers.values_list("id", flat=True))
        new_layer_ids = set(self.layer_ids).difference(existing_layer_ids)
        self.conversation.layers.add(
            *self.layers.filter(id__in=new_layer_ids).only("id")
        )

        logger.debug(f"[ChatGPT] CONVERSATION_ID: {self.conversation.id}")
        logger.debug(
            f"[ChatGPT] CONVERSATION_ID: {self.conversation.id}, USER: {self.user}"
        )
        logger.debug(
            f"CONVERSATION_ID: {self.conversation.id}, USER_INPUT: {self.user_input}"
        )
        self.message = MessageData(
            user_input=self.user_input, sql_query="", sql_result="", final_result=""
        )

        # Collect json_schemas and layer_ids from all layers
        json_schemas = [layer.json_schema for layer in self.layers]

        self.init_conversation = [
            {
                "role": "system",
                "content": get_sql_prompt(
                    json_schemas=json_schemas, layer_ids=self.layer_ids
                ),
            }
        ]

    def natural_language_to_sql_prompt(self) -> str:
        """
        prepare the user input and send it to ChatGPT to get the SQL query
        """
        # Collect json_schemas and layer_ids from all layers
        json_schemas = [layer.json_schema for layer in self.layers]

        full_conversation = self.reduce_sql_prompts(
            current_conversation=[
                {
                    "role": "system",
                    "content": get_sql_prompt(
                        json_schemas=json_schemas, layer_ids=self.layer_ids
                    ),
                },
                {"role": "user", "content": self.user_input},
            ],
        )
        try:
            response = client.chat.completions.create(
                model=self.gpt_model,
                messages=full_conversation,
                temperature=0.2,
                max_tokens=settings.SQL_GENERATOR_MAX_TOKENS,
                timeout=10,
            )
        except Exception as e:
            logger.debug(
                f"[ChatGPT] [natural_language_to_sql_prompt] - CONVERSATION_ID: {self.conversation.id} - ERROR: {e}"
            )
            return ""

        self.message["sql_query"] = sql_query = extract_sql_query(
            response.choices[0].message.content
        )
        logger.debug(
            f"[ChatGPT] [natural_language_to_sql_prompt] CONVERSATION_ID: {self.conversation.id},"
            f" SQL_QUERY: {sql_query}"
        )
        self.init_conversation.append(response.choices[0].message.dict())
        return sql_query

    def reduce_sql_prompts(
        self, current_conversation, tokens_count: int = settings.SQL_GENERATOR_TOKENS
    ) -> list[dict]:
        """
        minimizing the conversation list as long as its tokens are larger than the tokens_count,
        so we never exceed the requests maximum tokens
        """
        init_conversation = self.init_conversation[1:]
        full_conversation = init_conversation + current_conversation
        while (
            num_tokens_from_string(self.gpt_model, json.dumps(full_conversation))
            > tokens_count
            and init_conversation
        ):
            init_conversation = init_conversation[2:]
            full_conversation = init_conversation + current_conversation
        return full_conversation

    def run_raw_sql(self, sql_query: str) -> list:
        """
        Execute the SQL Query that is created from ChatGPT and returns results with column names.

        :param sql_query: The SQL query to execute.
        :return: A list of dictionaries, each representing a row with column names as keys.
        """
        if "layer_id" not in sql_query:
            logger.debug(
                f"[ChatGPT] [run_raw_sql] CONVERSATION_ID: {self.conversation.id}, sql_query has no layer_id"
            )
            return []

        # Check if the SQL query contains at least one of our layer IDs
        if not any(f"{layer_id}" in sql_query for layer_id in self.layer_ids):
            logger.debug(
                f"[ChatGPT] [run_raw_sql] CONVERSATION_ID: {self.conversation.id}, sql_query does not reference any of our layer_ids"
            )
            return []

        results = []
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query)
                rows = cursor.fetchall()[:10]  # Limit to the first 10 rows

                # Extract column names from cursor description
                column_names = [col[0] for col in cursor.description]

                # Combine rows with column names into dictionaries
                results = [dict(zip(column_names, row)) for row in rows]

                self.message["sql_result"] = results
        except Exception as e:
            logger.debug(
                f"[ChatGPT] [run_raw_sql] CONVERSATION_ID: {self.conversation.id}, "
                f"SQL_ERROR: {e}"
            )
            self.message["sql_result"] = f"SQL Error: {e}"
        logger.debug(
            f"[ChatGPT] [run_raw_sql] CONVERSATION_ID: {self.conversation.id}, "
            f"DB_RESULT = {results}"
        )
        return results

    def db_result_to_professional_language_prompt(self, db_result: list):
        """
        send the SQL Query & user input to ChatGPT to create a presentable meaningful phrase
        """
        if not db_result and "SQL Error" in self.message["sql_result"]:
            full_conversation = self.reduce_sql_prompts(
                tokens_count=settings.FINAL_RESULT_TOKENS,
                current_conversation=[
                    {"role": "system", "content": FORMAT_PROMPT_GENERAL_QUESTION},
                    {"role": "system", "content": f"USER INPUT: {self.user_input}"},
                ],
            )
        else:
            full_conversation = self.reduce_final_result_conversation_prompts(
                db_result=db_result
            )

        try:
            response = client.chat.completions.create(
                model=self.gpt_model,
                messages=full_conversation,
                temperature=0.2,
                max_tokens=settings.FINAL_RESULT_MAX_TOKENS,
                timeout=10,
            )
        except Exception as e:
            logger.debug(
                f"[ChatGPT] [db_result_to_professional_language_prompt] - CONVERSATION_ID: {self.conversation.id}"
                f" - ERROR: {e}"
            )
            return "عذراً، لا يتوفر لدينا بيانات حول الحوادث في الوقت الحالي."

        self.message["final_result"] = final_result = response.choices[
            0
        ].message.content
        logger.debug(
            f"[ChatGPT] [db_result_to_professional_language_prompt] CONVERSATION_ID: {self.conversation.id}, "
            f"FINAL_RESULT = {final_result}"
        )
        if "SELECT" in final_result:
            return "عذراً، لا يتوفر لدينا بيانات في الوقت الحالي."
        return final_result

    def reduce_final_result_conversation_prompts(
        self, db_result: list, tokens_count: int = settings.FINAL_RESULT_TOKENS
    ) -> list[dict]:
        """
        minimizing the conversation list as long as its tokens are larger than the tokens_count,
        so we never exceed the requests maximum tokens
        """
        current_conversation = [
            {"role": "system", "content": FORMAT_PROMPT},
            {
                "role": "system",
                "content": f"USER INPUT: {self.user_input}, DATABASE RESULT: {db_result}",
            },
        ]
        init_conversation = self.init_conversation[1:]
        full_conversation = init_conversation + current_conversation

        while (
            num_tokens_from_string(self.gpt_model, json.dumps(full_conversation))
            > tokens_count
        ):
            if len(db_result) > 5:
                db_result = db_result[:5]
                full_conversation = init_conversation + [
                    {"role": "system", "content": FORMAT_PROMPT_LARGE_DATA},
                    {
                        "role": "system",
                        "content": f"USER INPUT: {self.user_input}, DATABASE RESULT: {db_result}",
                    },
                ]
            else:
                full_conversation = [
                    {"role": "system", "content": FORMAT_PROMPT},
                    {
                        "role": "system",
                        "content": f"USER INPUT: {self.user_input}, DATABASE RESULT: {db_result}",
                    },
                ]

        self.message["sql_result"] = db_result
        return full_conversation

    def create_conversation_message(self):
        return Message.objects.create(
            conversation=self.conversation,
            message=encoding_json_data(self.message),
        )

    def execute(self, question_type) -> Tuple[str, list]:
        from chat_ai.schema.input_object_types import EXECUTION_MAPPER

        return EXECUTION_MAPPER[question_type](self)()

    def execute_data_question(self) -> Tuple[str, list]:
        """
        Calling the model method in the right order
        """
        query = self.natural_language_to_sql_prompt()
        db_result = self.run_raw_sql(query)
        response = self.db_result_to_professional_language_prompt(db_result=db_result)
        self.create_conversation_message()
        return response, encoding_json_data(db_result)

    def execute_gis_question(self):
        ...

    def execute_general_question(self):
        ...

    def execute_data_action_order(self):
        ...


def encoding_json_data(data):
    return json.loads(JSONRenderer().render(data=data))


def num_tokens_from_string(gpt_model: str, text: str) -> int:
    """
    Calculating the tokens of a given string according to a specific GPT model
    """
    encoding = encoding_for_model(gpt_model)
    num_tokens = int(len(encoding.encode(text)) / 2.5)
    return num_tokens


def parse_source_properties(value):
    if not value:
        return value
    try:
        # Replace invalid escape sequences and parse JSON
        cleaned_value = value.replace("\\'", "'").replace("'", "'").replace("'", '"')
        return json.loads(cleaned_value)
    except json.JSONDecodeError:
        return None  # Handle invalid JSON gracefully


def extract_sql_query(result: str) -> str:
    """
    Extracting the SQL Query from GPT response depending on a specific pattern
    """
    match = re.search(pattern="(?s)(SELECT(?:.|\n)*?;)", string=result)
    return match.group() if match else result
