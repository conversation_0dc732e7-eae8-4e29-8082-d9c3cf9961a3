# Generated by Django 3.2.25 on 2025-06-26 08:35

from django.db import migrations


def migrate_eda_reports_from_layer_to_dataset(apps, schema_editor):
    EDAReport = apps.get_model("workspaces", "EDAReport")
    eda_reports = EDAReport.objects.only("layer")
    for eda_report in eda_reports:
        eda_report.dataset = eda_report.layer.dataset
    EDAReport.objects.bulk_update(objs=eda_reports, fields=["dataset"])


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0021_edareport_dataset"),
    ]

    operations = [
        migrations.RunPython(
            migrate_eda_reports_from_layer_to_dataset, migrations.RunPython.noop
        )
    ]
