import json
import random
import re
from json import JSONDecodeError


def slice_list_to_chunks(instances, chunk_size: int = 1000) -> list:
    """
    Slicing an iterable of objects into chunks of specified size
    """
    offset = 0
    records_set = instances[offset : offset + chunk_size]
    while records_set:
        yield records_set
        offset += chunk_size
        records_set = instances[offset : offset + chunk_size]


def fetch_queryset_in_chunks(queryset, batch_size=1000):
    batch = []
    for book in queryset.iterator(chunk_size=batch_size):
        batch.append(book)
        if len(batch) == batch_size:
            yield batch
            batch = []
    if batch:
        yield batch


def remove_null_values(data):
    """Recursively remove keys with null values from a nested dictionary."""
    if isinstance(data, dict):
        for key, value in list(data.items()):
            if value is None:
                del data[key]
            elif isinstance(value, (dict, list)):
                remove_null_values(value)
            if isinstance(value, list) and not any(value):
                del data[key]
    elif isinstance(data, list):
        for item in data:
            remove_null_values(item)
            if isinstance(item, dict) and not any(item.values()):
                data.remove(item)
    return data


def get_random_color() -> str:
    # Generate a random color in hex format
    return "#{:06x}".format(random.randint(0, 0xFFFFFF))


def get_value_from_dict(data, key):
    if not isinstance(data, dict):
        return None
    if key in data:
        return data[key]
    for value in data.values():
        if isinstance(value, dict):
            nested_result = get_value_from_dict(value, key)
            if nested_result is not None:
                return nested_result
    return None


def parse_all_properties(properties: dict) -> dict:
    """Attempt to parse all property values into JSON."""
    for key, value in list(properties.items()):
        try:
            properties[key] = json.loads(
                value.replace("'", '"')
                .replace("None", "null")
                .replace("True", "true")
                .replace("False", "false")
            )
        except (JSONDecodeError, TypeError, AttributeError):
            continue
    return properties


def parse_list_properties(properties: list) -> list:
    updated_properties = []
    for item in properties:
        try:
            updated_properties.append(
                json.loads(
                    item.replace("'", '"')
                    .replace("None", "null")
                    .replace("True", "true")
                    .replace("False", "false")
                )
            )
        except (JSONDecodeError, TypeError, AttributeError):
            updated_properties.append(item)
    return updated_properties


def parse_one_property(one_property: str) -> dict:
    """Attempt to parse one property value into JSON."""
    try:
        return json.loads(
            one_property.replace("'", '"')
            .replace("'", '"')
            .replace("None", "null")
            .replace("True", "true")
            .replace("False", "false")
        )
    except (JSONDecodeError, TypeError, AttributeError):
        return dict()


def convert_to_slug(s):
    """
    Convert a string into a valid slug based on Django's SlugField rules.
    :param s: The input string.
    :return: A string matching ^[-a-zA-Z0-9_]+$ regex pattern.
    """
    # Step 1: Replace invalid characters with spaces (keep only alphanumerics, hyphen, and underscore)
    sanitized = re.sub(r"[^a-zA-Z0-9_\-]+", " ", s)

    # Step 2: Replace spaces (result of invalid characters) with hyphens
    slug = re.sub(r"\s+", "-", sanitized)

    # Step 3: Optionally convert to lowercase
    final_slug = slug.strip().lower()

    return final_slug
