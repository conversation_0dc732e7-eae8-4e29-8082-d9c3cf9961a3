import graphene
from graphene_django import DjangoObjectType
from graphene_gis.scalars import GISScalar, JSONScalar

from common.utils import extract_fields_from_json_schema
from layers.models import Layer, Record, SLD
from workspaces.schema.utils import mapping_columns_with_json_schema_title


class SLDType(DjangoObjectType):
    feature_style = graphene.Field(JSONScalar)

    class Meta:
        model = SLD
        fields = ["id", "title", "sld_type", "feature_style"]


class LayerType(DjangoObjectType):
    id = graphene.Int()
    location_field_mapping = graphene.Field(JSONScalar)
    json_schema = graphene.Field(JSONScalar)
    web_ui_json_schema = graphene.Field(JSONScalar)
    data = graphene.Field(JSONScalar)
    boundaries = graphene.Field(GISScalar)
    layer_key = graphene.String()
    summary_fields = graphene.Field(JSONScalar)
    columns = graphene.Field(JSONScalar)
    titled_columns = graphene.Field(JSONScalar)
    sample_data = graphene.Field(JSONScalar)
    slds = graphene.List(SLDType)
    records_count = graphene.Int()
    data_fields = graphene.List(JSONScalar)
    filters = graphene.Field(JSONScalar)

    class Meta:
        model = Layer
        fields = [
            "id",
            "layer_key",
            "title",
            "description",
            "read_only",
            "location_field_mapping",
            "json_schema",
            "web_ui_json_schema",
            "data",
            "dataset",
            "status",
            "columns",
            "titled_columns",
            "slds",
        ]

    def resolve_layer_key(self: Layer, info):
        return self.key

    def resolve_summary_fields(self: Layer, info):
        return self.data.get("summary_fields") or self.dataset.meta_data.get(
            "summary_fields"
        )

    def resolve_columns(self: Layer, info):
        return self.data.get("columns") or self.dataset.meta_data.get("columns")

    def resolve_titled_columns(self: Layer, info):
        columns = self.data.get("columns") or self.dataset.meta_data.get("columns")
        return mapping_columns_with_json_schema_title(columns, self.json_schema)

    def resolve_sample_data(self: Layer, info):
        return self.dataset.meta_data.get("sample_data") if self.dataset else None

    def resolve_slds(self: Layer, info):
        return self.slds.all()

    def resolve_records_count(self: Layer, info):
        return self.records_count

    def resolve_data_fields(self: Layer, info):
        return extract_fields_from_json_schema(self.json_schema)


class LayerListType(graphene.ObjectType):
    data = graphene.List(LayerType)
    count = graphene.Int()


class RecordType(DjangoObjectType):
    id = graphene.Int()
    geometry = graphene.Field(GISScalar)
    layer_id = graphene.Int()
    map_data = graphene.Field(JSONScalar)
    data = graphene.Field(JSONScalar)
    source_properties = graphene.Field(JSONScalar)

    class Meta:
        model = Record
        fields = (
            "id",
            "layer_id",
            "map_data",
            "data",
            "source_properties",
            "created",
            "modified",
        )


class RecordListType(graphene.ObjectType):
    data = graphene.List(RecordType)
    count = graphene.Int()
