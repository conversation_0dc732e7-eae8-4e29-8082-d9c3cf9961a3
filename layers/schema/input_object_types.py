import graphene


class UpdateRecordsSummaryFieldsInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)
    map_data_columns = graphene.JSONString(required=True)


class CreateRecordInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)
    geometry = graphene.String(required=True)
    form_data = graphene.JSONString(required=True)


class UpdateRecordInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    record_id = graphene.Int(required=True)
    form_data = graphene.JSONString(required=True)


class SLDInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)
    color = graphene.String(required=True)
    opacity = graphene.Float(required=True)
    stroke_color = graphene.String()
    stroke_width = graphene.Int()
    stroke_opacity = graphene.Float()


class UpdateLayerInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)
    title = graphene.String()
    color = graphene.String()
    opacity = graphene.Float()
    read_only = graphene.Boolean()
    json_schema = graphene.JSONString()


class CreateLayerFromDatasetInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    dataset_id = graphene.ID(required=True)
    workspace_id = graphene.ID(required=True)


class DeleteLayerInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)


class DuplicateLayerInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)


class HeatMapInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)
    weight_field = graphene.String()
    color_range = graphene.List(graphene.String)
    opacity = graphene.Float()
    radius = graphene.Float()
    density_range = graphene.List(graphene.Float)


class UpdateLayerFiltersInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    layer_id = graphene.Int(required=True)
    filters = graphene.JSONString(required=True, description="JSON string of filters")
