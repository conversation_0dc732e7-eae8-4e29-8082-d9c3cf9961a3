import graphene
from django.utils import timesince
from graphene_django import DjangoObjectType
from graphene_gis.scalars import JSO<PERSON>calar

from common.utils import CustomJSONScalar
from users.schema import UserType
from workspaces.models import Workspace, EDAReport
from workspaces.models import WorkspaceRequest, Dataset


class WorkspaceLayersData(graphene.ObjectType):
    layers_count = graphene.Int()
    records_count = graphene.Int()


class WorkspaceType(DjangoObjectType):
    id = graphene.Int()
    since = graphene.String()
    layers_sorted_ids = graphene.List(graphene.Int)
    layers_data = graphene.Field(WorkspaceLayersData)

    class Meta:
        model = Workspace
        fields = [
            "id",
            "name",
            "description",
            "thumbnail",
            "since",
            "created",
            "modified",
            "last_visited",
            "layers_sorted_ids",
            "layers_data",
            "workspace_type",
        ]

    @classmethod
    def resolve_since(cls, workspace: Workspace, info):
        return timesince.timesince(workspace.modified, depth=1)


class WorkspaceListType(graphene.ObjectType):
    data = graphene.List(WorkspaceType)
    count = graphene.Int()


class DatasetType(DjangoObjectType):
    id = graphene.Int()
    meta_data = graphene.Field(JSONScalar)

    class Meta:
        model = Dataset
        fields = ["id", "title", "file", "meta_data", "workspace", "created"]


class WorkspaceRequestType(DjangoObjectType):
    id = graphene.Int()
    layer_data = graphene.Field(JSONScalar)
    dataset = graphene.Field(DatasetType)
    created_by = graphene.Field(UserType)
    current_step = graphene.Int()

    class Meta:
        model = WorkspaceRequest
        fields = ["id", "status", "request_type", "created_by", "dataset", "layer_data"]


class WorkspaceRequestListType(graphene.ObjectType):
    data = graphene.List(WorkspaceRequestType)
    count = graphene.Int()


class PreviewDataType(graphene.ObjectType):
    column = graphene.String()
    title = graphene.String()
    data = graphene.List(CustomJSONScalar)


class PreviewDataListType(graphene.ObjectType):
    data = graphene.List(PreviewDataType)


class JsonSchemasType(graphene.ObjectType):
    form_data = graphene.List(JSONScalar)
    json_schema = graphene.Field(JSONScalar)


class DatasetListType(graphene.ObjectType):
    data = graphene.List(DatasetType)
    count = graphene.Int()


class EDAReportType(DjangoObjectType):
    class Meta:
        model = EDAReport
        fields = ["id", "file", "source", "created"]


class EDAReportListType(graphene.ObjectType):
    data = graphene.List(EDAReportType)
    count = graphene.Int()
