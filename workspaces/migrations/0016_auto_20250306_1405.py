# Generated by Django 3.2.25 on 2025-03-06 14:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0015_rename_datasetrequest_workspacerequest"),
    ]

    operations = [
        migrations.AddField(
            model_name="workspacerequest",
            name="request_type",
            field=models.CharField(
                choices=[
                    ("upload_file", "Upload File"),
                    ("design_layer", "Design Layer"),
                    ("connect_db", "Connect Database"),
                ],
                db_index=True,
                default="upload_file",
                max_length=20,
                verbose_name="Request Type",
            ),
        ),
        migrations.AlterField(
            model_name="workspacerequest",
            name="dataset",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="requests",
                to="workspaces.dataset",
                verbose_name="Dataset",
            ),
        ),
    ]
