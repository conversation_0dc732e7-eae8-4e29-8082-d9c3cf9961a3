# Generated by Django 3.2.25 on 2024-11-26 09:54

from django.db import migrations


def fill_layers_data_with_summary_fields(apps, schema_editor):
    Layer = apps.get_model("layers", "Layer")
    layers = Layer.objects.only("id", "data")
    for layer in layers:
        if record := layer.records.filter(map_data__isnull=False).first():
            layer.data["summary_fields"] = list(record.map_data.keys())
    Layer.objects.bulk_update(objs=layers, fields=["data"])


class Migration(migrations.Migration):

    dependencies = [("layers", "0005_layer_data")]

    operations = [
        migrations.RunPython(
            fill_layers_data_with_summary_fields, migrations.RunPython.noop
        )
    ]
