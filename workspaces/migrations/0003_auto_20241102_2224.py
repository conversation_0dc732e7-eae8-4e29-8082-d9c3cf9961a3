# Generated by Django 3.2.25 on 2024-11-02 22:24

import django.core.validators
import django.utils.timezone
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0012_alter_organization_options"),
        ("layers", "0001_initial"),
        ("workspaces", "0002_alter_workspace_thumbnail"),
    ]

    operations = [
        migrations.AddField(
            model_name="dataset",
            name="created",
            field=django_extensions.db.fields.CreationDateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="created",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="dataset",
            name="meta_data",
            field=jsoneditor.fields.django3_jsonfield.JSONField(
                blank=True, default=dict, verbose_name="Meta Data"
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="dataset",
            name="modified",
            field=django_extensions.db.fields.ModificationDateTimeField(
                auto_now=True, verbose_name="modified"
            ),
        ),
        migrations.AddField(
            model_name="workspace",
            name="organization",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="workspaces",
                to="organizations.organization",
                verbose_name="Organization",
            ),
        ),
        migrations.AlterField(
            model_name="dataset",
            name="file",
            field=models.URLField(
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["csv", "gpkg", "geojson", "kml", "shp"]
                    )
                ],
                verbose_name="Original Dataset File",
            ),
        ),
        migrations.AlterField(
            model_name="datasetrequest",
            name="dataset",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="requests",
                to="workspaces.dataset",
                verbose_name="Dataset",
            ),
        ),
        migrations.AlterField(
            model_name="datasetrequest",
            name="layer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="requests",
                to="layers.layer",
                verbose_name="Layer",
            ),
        ),
        migrations.AlterField(
            model_name="datasetrequest",
            name="status",
            field=models.CharField(
                choices=[
                    ("in_progress", "In Progress"),
                    ("finished", "Finished"),
                    ("cancelled", "Cancelled"),
                ],
                db_index=True,
                default="in_progress",
                max_length=20,
                verbose_name="Request Status",
            ),
        ),
        migrations.AlterField(
            model_name="workspace",
            name="last_visited",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Last Visited"
            ),
        ),
        migrations.DeleteModel(
            name="Layer",
        ),
    ]
