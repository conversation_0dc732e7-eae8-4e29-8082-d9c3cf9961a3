import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequest, WorkspaceRequestChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestCancelWorkspaceRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        )
        # Create a workspace request in IN_PROGRESS state
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        self.mutation = """
        mutation MyMutation($datasetRequestId: Int!, $orgId: Int!) {
          cancelWorkspaceRequest(closeInput: {datasetRequestId: $datasetRequestId, orgId: $orgId}) {
            closed
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot cancel a workspace request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_dataset_request_id(self):
        """Test that an invalid dataset request ID is handled properly."""
        # Use a non-existent dataset request ID
        invalid_variables = self.variables.copy()
        invalid_variables["datasetRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # The mutation should execute but not find any requests to cancel
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["cancelWorkspaceRequest"]["closed"])

        # Verify the original request is still in progress
        dataset_request = WorkspaceRequest.objects.get(id=self.dataset_request.id)
        self.assertEqual(dataset_request.status, WorkspaceRequestChoices.IN_PROGRESS)

    def test_already_cancelled_request(self):
        """Test that an already cancelled request is handled properly."""
        # Cancel the request first
        self.dataset_request.status = WorkspaceRequestChoices.CANCELLED
        self.dataset_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # The mutation should execute but not find any IN_PROGRESS requests to cancel
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["cancelWorkspaceRequest"]["closed"])

        # Verify the request is still cancelled
        dataset_request = WorkspaceRequest.objects.get(id=self.dataset_request.id)
        self.assertEqual(dataset_request.status, WorkspaceRequestChoices.CANCELLED)

    def test_other_user_request_regular_user(self):
        """Test that a regular user cannot cancel another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )

        # Try to cancel the other user's request
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # The mutation should execute but not find any requests that match the criteria
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["cancelWorkspaceRequest"]["closed"])

        # Verify the other user's request is still in progress
        other_user_request.refresh_from_db()
        self.assertEqual(other_user_request.status, WorkspaceRequestChoices.IN_PROGRESS)

    def test_other_user_request_superuser(self):
        """Test that a superuser can cancel another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )

        # Try to cancel the other user's request as a superuser
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["cancelWorkspaceRequest"]["closed"])

        # Verify the other user's request is now cancelled
        other_user_request.refresh_from_db()
        self.assertEqual(other_user_request.status, WorkspaceRequestChoices.CANCELLED)

    def test_successful_cancellation(self):
        """Test successful cancellation of a workspace request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["cancelWorkspaceRequest"]["closed"])

        # Verify the request is now cancelled
        dataset_request = WorkspaceRequest.objects.get(id=self.dataset_request.id)
        self.assertEqual(dataset_request.status, WorkspaceRequestChoices.CANCELLED)
