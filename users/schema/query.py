from collections import defaultdict

import graphene
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from gabbro.acl.models import Role
from gabbro.graphene import BadRequest

from common.utils import (
    organization_required,
    PageInfo,
    DjangoFilterInput,
    filter_qs_paginate_with_count,
    build_q,
    authorize_user,
)
from common.utils.graphene import authentication_required
from common.utils.models import get_owner_user
from organizations.models import Organization
from organizations.perms_constants import (
    VIEW_USER,
    CHANGE_WORKSPACE,
    VIEW_ROLE,
    ADD_ROLE,
)
from users.models import ActiveStatusChoices
from users.schema.object_types import (
    UserType,
    RoleListType,
    PermissionModelType,
    UserListType,
    WorkspacePermissionType,
)
from workspaces.models import Workspace

User = get_user_model()


class Query(graphene.ObjectType):
    user_details = graphene.Field(UserType)
    users = graphene.Field(
        UserListType,
        org_id=graphene.Int(required=True),
        pk=graphene.Int(),
        workspace_id=graphene.Int(),
        exclude_individuals=graphene.Boolean(default_value=False),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    roles = graphene.Field(
        RoleListType,
        org_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    permissions = graphene.List(PermissionModelType, org_id=graphene.Int(required=True))
    workspace_permissions = graphene.List(
        WorkspacePermissionType, org_id=graphene.Int(required=True)
    )

    @staticmethod
    @authentication_required
    def resolve_user_details(root, info, **kwargs):
        return info.context.user

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_users(
        root,
        info,
        org_id: int,
        exclude_individuals,
        pk=None,
        workspace_id=None,
        page_info=None,
        filters=None,
        **kwargs,
    ):
        """
        Resolves the users based on the provided organizational and workspace context
        through ensuring accessible and coherent results.
        """
        user = info.context.user
        organization = info.context.organization

        # Authorize the user's access to the organization
        authorize_user(model_obj=organization, user=user, permission=VIEW_USER)

        # Retrieve the organization owner
        owner_user = get_owner_user(organization)
        if not owner_user:
            raise BadRequest(reason={"user": _("No owner user found") % {}})

        # queries all individuals (users) associated with the organization
        # Exclude Deleted Users & the main owner
        organization_users = organization.acl_individuals.exclude(
            Q(active_status=ActiveStatusChoices.DELETED) | Q(pk=owner_user.pk)
        ).distinct()

        # Apply workspace-specific filtering, if applicable
        workspace = Workspace.objects.filter(
            id=workspace_id, organization=organization
        ).first()
        if workspace:
            # Authorize the user permission on the workspace
            authorize_user(
                model_obj=organization, user=user, permission=CHANGE_WORKSPACE
            )
            # Filter users based on workspace membership
            organization_users = filter_queryset_by_workspace(
                workspace, organization_users, owner_user, exclude_individuals
            )

        # Paginate and prepare the final result
        users, total_users = filter_qs_paginate_with_count(
            organization_users, build_q(pk, filters), page_info
        )
        setattr(info.context, "organization", organization)
        setattr(info.context, "workspace", workspace)

        return UserListType(data=users, count=total_users)

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_roles(
        root, info, org_id: int, pk=None, page_info=None, filters=None, **kwargs
    ):
        user = info.context.user
        organization: Organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=VIEW_ROLE)
        queryset = organization.roles.exclude(
            codename=settings.MAIN_OWNER_ROLE_CODENAME
        )
        data, count = filter_qs_paginate_with_count(
            queryset, build_q(pk, filters), page_info
        )
        setattr(info.context, "organization", organization)
        return RoleListType(data=data, count=count)

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_permissions(root, info, org_id: int, **kwargs):
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=ADD_ROLE)
        permissions = Permission.objects.filter(
            (Q(content_type__model="organization"))
            & (
                Q(codename__endswith="_user")
                | Q(codename__endswith="_workspace")
                | Q(codename__endswith="_role")
            )
        )

        grouped_permissions = defaultdict(list)
        for permission in permissions:
            model = permission.codename.split("_")[1]
            grouped_permissions[model].append(permission)
        grouped_permissions = [
            {"model": model, "permissions": permissions}
            for model, permissions in grouped_permissions.items()
        ]
        return grouped_permissions

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_workspace_permissions(root, info, org_id: int, **kwargs):
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=CHANGE_WORKSPACE)
        roles = Role.objects.filter(codename__in=settings.WORKSPACE_ROLES_CODENAMES)
        return roles


def filter_queryset_by_workspace(
    workspace: Workspace, queryset, owner_user: User, exclude_individuals: bool
):
    """
    Filters the queryset based on workspace and exclusion rules.
    """
    if exclude_individuals:
        return queryset.exclude(
            id__in=workspace.acl_individuals.distinct().values_list("id", flat=True)
        )
    return workspace.acl_individuals.exclude(
        Q(active_status=ActiveStatusChoices.DELETED) | Q(pk=owner_user.pk)
    ).distinct()
