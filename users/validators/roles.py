from django.conf import settings
from django.utils.translation import gettext_lazy as _
from gabbro.acl.models import Role
from gabbro.graphene import NotFound

from common.interfaces import InputValidation
from organizations.models import Organization
from users.mixins import RoleMixin, UserMixin
from users.models import User
from workspaces.models import Workspace


class ChangeUserRoleValidation(InputValidation, RoleMixin, UserMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        role = self.get_role_if_exists(
            role_id=data_input["role_id"], organization=organization
        )
        user_target = self.get_organization_user_if_exists(
            user_id=data_input["user_id"], organization=organization
        )
        return dict(role=role, user_target=user_target)


class AssignWorkspaceUserPermissionsValidation(InputValidation, UserMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        permissions_ids = data_input["permissions_ids"]

        workspace = Workspace.objects.filter(
            id=data_input["workspace_id"], organization=organization
        ).first()
        if not workspace:
            raise NotFound(
                reason={
                    "user_id": _("Invalid workspace %(workspace_id)s")
                    % {"workspace_id": data_input["workspace_id"]}
                }
            )

        users = organization.acl_individuals.filter(
            id__in=data_input["users_ids"]
        ).distinct()
        if not users:
            raise NotFound(
                reason={
                    "user_id": _("Invalid users %(users_ids)s")
                    % {"users_ids": data_input["users_ids"]}
                }
            )

        workspace_roles = []
        if permissions_ids:
            workspace_roles = Role.objects.filter(
                id__in=permissions_ids,
                codename__in=getattr(settings, "WORKSPACE_ROLES_CODENAMES", []),
            )
            if not workspace_roles or len(workspace_roles) != len(permissions_ids):
                raise NotFound(
                    reason={
                        "user_id": _("Invalid permissions %(permissions_ids)s")
                        % {"permissions_ids": permissions_ids}
                    }
                )
        return dict(workspace=workspace, users=users, roles=workspace_roles)
