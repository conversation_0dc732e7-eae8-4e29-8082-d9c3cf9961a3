import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.handlers.dataset_files import DatasetFilesLoader
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from common.utils.json_schema import get_predicted_jsonschema
from workspaces.models import WorkspaceRequest, WorkspaceRequestChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestJsonSchemas(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(
                BASE_DIR, "common", "tests", "fixtures", "sample_geojson.csv"
            )
        )
        self.dataset_requests = WorkspaceRequestFactory.create_batch(
            5,
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            layer_data={
                "color": "#42FAC2",
                "title": "test dataset",
                "created": "2024-12-06T19:07:40",
                "read_only": False,
                "description": "ny desc",
                "json_schema": {},
                "web_ui_json_schema": None,
                "location_field_mapping": {
                    "coordinate_type": "point",
                    "lang_lat_column": "",
                    "latitude_column": "X",
                    "longitude_column": "Y",
                    "lat_lon_column_num": "two_column",
                },
            },
        )
        self.query = """
        query MyQuery($datasetRequestId: Int!, $orgId: Int!) {
          jsonSchemas(datasetRequestId: $datasetRequestId, orgId: $orgId) {
            jsonSchema
            formData
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_requests[0].id,
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        geometry_columns = [
            "Y",
            "X",
        ]

        # Loading data from dataset file
        loader = DatasetFilesLoader(
            file=self.dataset_requests[0].dataset.file,
            excluded_columns=geometry_columns,
        )
        data = loader.get_all_features()
        # Get the predicted schema from dataset data
        json_schema = get_predicted_jsonschema(data, len(data) > 20)
        self.assertNotIn("errors", response)
        schema = response["data"]["jsonSchemas"]
        self.assertListEqual(
            schema["formData"],
            data[: settings.DATASET_SAMPLE_ROWS_NUMBER],
        )
        self.assertDictEqual(
            schema["jsonSchema"],
            json_schema,
        )

    def test_query_with_superuser(self):
        user = UserFactory(is_superuser=False)
        WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=user,
            organization=self.organization,
        )
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

    def test_query_with_invalid_dataset_request(self):
        WorkspaceRequest.objects.all().delete()
        # invalid_dataset_request_status
        WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.FINISHED,
            organization=self.organization,
        )
        # invalid_dataset_request_layer_data
        WorkspaceRequestFactory(
            dataset=self.dataset,
            organization=self.organization,
            created_by=self.user,
            layer_data={},
        )
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "datasetRequestId": _("Invalid dataset_request_id: %(id)s")
                % {"id": self.variables["datasetRequestId"]}
            },
        )

    def test_query_with_one_column_value(self):
        dataset = DatasetFactory(
            file=os.path.join(
                BASE_DIR, "common", "tests", "fixtures", "sample_geojson.geojson"
            )
        )
        dataset_request = WorkspaceRequestFactory(
            dataset=dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            layer_data={
                "color": "#ff0000",
                "title": "معالم جده",
                "created": "2024-12-12T08:17:41",
                "read_only": True,
                "description": "",
                "json_schema": {
                    "type": "object",
                    "$schema": "http://json-schema.org/schema#",
                    "required": [
                        "city",
                        "confidence",
                        "location_type",
                        "name",
                        "parcel_id",
                        "poi_type",
                        "point_id",
                    ],
                    "properties": {
                        "city": {"type": "string"},
                        "name": {"type": "array", "items": {"type": "string"}},
                        "poi_type": {
                            "$ref": "#/definitions/poi_type",
                            "type": "string",
                        },
                        "point_id": {"type": "integer"},
                        "parcel_id": {"type": "integer"},
                        "confidence": {
                            "$ref": "#/definitions/confidence",
                            "type": "string",
                        },
                        "location_type": {
                            "$ref": "#/definitions/location_type",
                            "type": "string",
                        },
                    },
                    "definitions": {
                        "poi_type": {
                            "enum": ["sport", "schools", "mosques", "security"],
                            "type": "string",
                        },
                        "confidence": {
                            "enum": ["", "Medium", "High"],
                            "type": "string",
                        },
                        "location_type": {
                            "enum": ["edge", "center", "building"],
                            "type": "string",
                        },
                    },
                },
                "web_ui_json_schema": {},
                "location_field_mapping": {
                    "coordinate_type": "point",
                    "lang_lat_column": "geometry",
                    "latitude_column": "",
                    "longitude_column": "",
                    "lat_lon_column_num": "column",
                },
            },
        )
        self.variables["datasetRequestId"] = dataset_request.id
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        # Loading data from dataset file
        schema = response["data"]["jsonSchemas"]
        self.assertListEqual(
            schema["formData"],
            [
                {
                    "parcel_id": 1739,
                    "poi_type": "schools",
                    "location_type": "edge",
                    "city": "جدة",
                    "name": [
                        "مدرسة ابن البيطار الثانوية",
                        "ملعب مدرسة ابن البيطار الثانوية",
                    ],
                    "confidence": "High",
                    "point_id": 1,
                },
                {
                    "parcel_id": 1739,
                    "poi_type": "schools",
                    "location_type": "building",
                    "city": "جدة",
                    "name": [
                        "مدرسة ابن البيطار الثانوية",
                        "ملعب مدرسة ابن البيطار الثانوية",
                    ],
                    "confidence": "High",
                    "point_id": 110027,
                },
                {
                    "parcel_id": 1739,
                    "poi_type": "schools",
                    "location_type": "edge",
                    "city": "جدة",
                    "name": [
                        "مدرسة ابن البيطار الثانوية",
                        "ملعب مدرسة ابن البيطار الثانوية",
                    ],
                    "confidence": "High",
                    "point_id": 4,
                },
                {
                    "parcel_id": 1739,
                    "poi_type": "schools",
                    "location_type": "center",
                    "city": "جدة",
                    "name": [
                        "مدرسة ابن البيطار الثانوية",
                        "ملعب مدرسة ابن البيطار الثانوية",
                    ],
                    "confidence": "High",
                    "point_id": 57697,
                },
                {
                    "parcel_id": 1739,
                    "poi_type": "schools",
                    "location_type": "edge",
                    "city": "جدة",
                    "name": [
                        "مدرسة ابن البيطار الثانوية",
                        "ملعب مدرسة ابن البيطار الثانوية",
                    ],
                    "confidence": "High",
                    "point_id": 2,
                },
            ],
        )
        self.assertDictEqual(
            schema["jsonSchema"],
            {
                "$schema": "http://json-schema.org/schema#",
                "type": "object",
                "properties": {
                    "parcel_id": {"type": "integer"},
                    "poi_type": {"type": "string"},
                    "location_type": {"type": "string"},
                    "city": {"type": "string"},
                    "name": {"type": "array", "items": {"type": "string"}},
                    "confidence": {"type": "string"},
                    "point_id": {"type": "integer"},
                },
                "required": [
                    "city",
                    "confidence",
                    "location_type",
                    "name",
                    "parcel_id",
                    "poi_type",
                    "point_id",
                ],
            },
        )
