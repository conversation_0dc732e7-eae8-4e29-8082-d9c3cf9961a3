from rest_framework import serializers

from layers.models import Layer, SLD


class LayerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Layer
        fields = ["id", "title", "read_only", "json_schema", "filters"]


class HeatMapSerializer(serializers.Serializer):
    weight_field = serializers.CharField(max_length=255)
    color_range = serializers.ListField(
        child=serializers.CharField(max_length=7), min_length=2, max_length=2
    )
    opacity = serializers.FloatField(min_value=0.1, max_value=1.0)
    radius = serializers.IntegerField(min_value=1, max_value=10)
    density_range = serializers.ListField(
        child=serializers.FloatField(), min_length=2, max_length=2, required=False
    )


class SLDPointStyleSerializer(serializers.Serializer):
    color = serializers.CharField(max_length=7)
    opacity = serializers.FloatField(min_value=0.0, max_value=1.0)
    stroke_color = serializers.Char<PERSON>ield(
        max_length=7, required=False, allow_null=True, allow_blank=True
    )
    stroke_width = serializers.IntegerField(
        min_value=1,
        max_value=10,
        required=False,
        allow_null=True,
    )
    stroke_opacity = serializers.FloatField(
        min_value=0.0,
        max_value=1.0,
        required=False,
        allow_null=True,
    )

    def validate(self, attrs):
        color = attrs["color"]
        opacity = attrs["opacity"]
        attrs["stroke_color"] = attrs.get("stroke_color", color) or color
        attrs["stroke_width"] = attrs.get("stroke_width", 1) or 1
        attrs["stroke_opacity"] = attrs.get("stroke_opacity", opacity) or opacity
        return attrs


class SLDSerializer(serializers.ModelSerializer):
    feature_style = SLDPointStyleSerializer()

    class Meta:
        model = SLD
        fields = ["id", "title", "sld_type", "feature_style", "xml_body"]
