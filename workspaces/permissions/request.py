from common.interfaces import PermissionsInterface
from common.utils import authorize_user
from organizations.perms_constants import CHANGE_WORKSPACE, ADD_WORKSPACE
from workspaces.mixins import WorkspaceRequestMixin


class UpdateWorkspaceRequestPerms(PermissionsInterface, WorkspaceRequestMixin):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=CHANGE_WORKSPACE)
        self.authorize_workspace_request(
            user=user, workspace_request=context["dataset_request"]
        )


class CreateWorkspaceRequestPerms(PermissionsInterface):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=ADD_WORKSPACE)


class UpdateDesignLayerRequestPerms(PermissionsInterface, WorkspaceRequestMixin):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=CHANGE_WORKSPACE)
        self.authorize_workspace_request(
            user=user, workspace_request=context["workspace_request"]
        )


class DesignLayerJsonSchemaPerms(PermissionsInterface, WorkspaceRequestMixin):
    def check_permissions(self, user, organization, context: dict = None):
        authorize_user(user=user, model_obj=organization, permission=CHANGE_WORKSPACE)
        self.authorize_workspace_request(
            user=user, workspace_request=context["workspace_request"]
        )
