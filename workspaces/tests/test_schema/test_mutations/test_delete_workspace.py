import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from chat_ai.models import Conversation
from common.tests.factories import (
    WorkspaceFactory,
    BaseTestMixin,
    LayerFactory,
    UserFactory,
    ConversationFactory,
    DatasetFactory,
)
from workspaces.models import Workspace, EDAReport
from workspaces.models.eda_report import EDAReportSourceChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestDeleteWorkSpace(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace for testing
        self.workspace = WorkspaceFactory(
            owner=self.user,
            organization=self.organization,
            name="Test Workspace",
            description="Test Description",
        )

        # Create some layers for the workspace
        self.layer1 = LayerFactory(
            workspace=self.workspace,
            dataset=DatasetFactory(
                file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
            ),
        )
        self.layer2 = LayerFactory(
            workspace=self.workspace,
            dataset=DatasetFactory(
                file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
            ),
        )

        # Create a conversation associated with a layer
        self.conversation = ConversationFactory()
        self.conversation.layers.set([self.layer1])

        # Create an EDA report associated with a layer
        self.eda_report = EDAReport.objects.create(
            dataset=self.layer1.dataset,
            hash_code="test_hash",
            file="https://test.com/report.pdf",
            source=EDAReportSourceChoices.YDATA,
        )

        self.mutation = """
        mutation MyMutation($workspaceId: Int!, $orgId: Int!) {
          deleteWorkspace(
            dataInput: {workspaceId: $workspaceId, orgId: $orgId}
          ) {
            deleted
          }
        }
        """
        self.variables = {
            "workspaceId": self.workspace.id,
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot delete a workspace."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_workspace_id(self):
        """Test that an invalid workspace ID is handled properly."""
        # Use a non-existent workspace ID
        invalid_variables = self.variables.copy()
        invalid_variables["workspaceId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a workspace error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceId": "Workspace with 99999 not found"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_other_user_workspace_regular_user(self):
        """Test that a regular user can delete another user's workspace if they have the permission."""
        # Create a workspace owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace = WorkspaceFactory(
            owner=other_user,
            organization=self.organization,
            name="Other User Workspace",
            description="Other User Description",
        )

        # Create a layer for this workspace
        other_layer = LayerFactory(workspace=other_workspace)

        # Try to delete the other user's workspace
        other_variables = self.variables.copy()
        other_variables["workspaceId"] = other_workspace.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # The mutation should execute successfully because the user has the delete_workspace permission
        # on the organization, even though they are not the owner of the workspace
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["deleteWorkspace"]["deleted"])

        # Verify the workspace was deleted
        self.assertFalse(Workspace.objects.filter(id=other_workspace.id).exists())

        # Verify the layer was deleted
        self.assertFalse(
            other_layer.__class__.objects.filter(id=other_layer.id).exists()
        )

    def test_other_user_workspace_superuser(self):
        """Test that a superuser can delete another user's workspace."""
        # Create a workspace owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace = WorkspaceFactory(
            owner=other_user,
            organization=self.organization,
            name="Other User Workspace",
            description="Other User Description",
        )

        # Create a layer for this workspace
        other_layer = LayerFactory(workspace=other_workspace)

        # Try to delete the other user's workspace as a superuser
        other_variables = self.variables.copy()
        other_variables["workspaceId"] = other_workspace.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["deleteWorkspace"]["deleted"])

        # Verify the workspace was deleted
        self.assertFalse(Workspace.objects.filter(id=other_workspace.id).exists())

        # Verify the layer was deleted
        self.assertFalse(
            other_layer.__class__.objects.filter(id=other_layer.id).exists()
        )

    def test_successful_deletion(self):
        """Test successful deletion of a workspace."""
        # Get IDs before deletion for verification
        workspace_id = self.workspace.id
        layer1_id = self.layer1.id
        layer2_id = self.layer2.id
        conversation_id = self.conversation.id
        eda_report_id = self.eda_report.id

        # Delete the EDA report first to avoid PROTECT constraint
        self.eda_report.delete()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        self.assertTrue(response["data"]["deleteWorkspace"]["deleted"])

        # Verify the workspace was deleted
        self.assertFalse(Workspace.objects.filter(id=workspace_id).exists())

        # Verify the layers were deleted
        self.assertFalse(self.layer1.__class__.objects.filter(id=layer1_id).exists())
        self.assertFalse(self.layer2.__class__.objects.filter(id=layer2_id).exists())

        # Verify the conversation was deleted
        self.assertFalse(Conversation.objects.filter(id=conversation_id).exists())

        # Verify the EDA report was deleted
        self.assertFalse(EDAReport.objects.filter(id=eda_report_id).exists())
