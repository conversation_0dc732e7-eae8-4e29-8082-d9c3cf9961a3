from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.interfaces import InputValidation
from organizations.models import Organization
from users.mixins import RoleMixin
from users.models import User, ActiveStatusChoices


class AddUserValidation(InputValidation, RoleMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        role = self.get_role_if_exists(
            role_id=data_input["role_id"], organization=organization
        )
        return dict(role=role)


class ChangeUserActiveStatusValidation(InputValidation):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        active_status = data_input["active_status"]
        user_target = organization.acl_individuals.filter(
            id=data_input.pop("user_id")
        ).first()
        if not user_target:
            raise BadRequest(reason={"user_id": _("Invalid user_id") % {}})
        if (
            user_target.active_status == ActiveStatusChoices.WAITING
            and active_status
            in [ActiveStatusChoices.ACTIVE, ActiveStatusChoices.INACTIVE]
        ):
            raise BadRequest(
                reason={
                    "user_id": _("User is waiting, you can't change activation status")
                    % {}
                }
            )
        if active_status == ActiveStatusChoices.WAITING:
            raise BadRequest(
                reason={"user_id": _("Can't set activation to waiting") % {}}
            )
        return dict(user_target=user_target)
