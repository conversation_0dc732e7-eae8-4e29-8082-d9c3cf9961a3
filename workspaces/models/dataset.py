from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from common.utils import dataset_extension_validator


class Dataset(TimeStampedModel):
    workspace = models.ForeignKey(
        "workspaces.Workspace",
        on_delete=models.SET_NULL,
        related_name="datasets",
        null=True,
        verbose_name=_("Workspace"),
    )
    file = models.URLField(
        validators=[dataset_extension_validator],
        verbose_name=_("Original Dataset File"),
    )
    title = models.CharField(
        max_length=255, blank=True, default="", verbose_name=_("Title")
    )
    meta_data = J<PERSON>NField(blank=True, default=dict, verbose_name=_("Meta Data"))

    class Meta:
        verbose_name = _("Dataset")
        verbose_name_plural = _("Datasets")

    def __str__(self):
        return f"pk: {self.pk}"

    def set_metadata(self, metadata: dict):
        self.meta_data = {**self.meta_data, **metadata}
        self.save(update_fields=["meta_data", "modified"])

    def set_workspace(self, workspace):
        self.workspace = workspace
        self.save(update_fields=["workspace", "modified"])

    def create_eda_reports(self, eda_reports):
        from workspaces.models import EDAReport

        if not isinstance(eda_reports, list):
            return
        for eda_report_data in eda_reports:
            EDAReport.objects.create(
                dataset=self,
                hash_code=eda_report_data["hash"],
                file=eda_report_data["report_path"],
                created=eda_report_data["created_at"],
                source=eda_report_data["source"],
            )
