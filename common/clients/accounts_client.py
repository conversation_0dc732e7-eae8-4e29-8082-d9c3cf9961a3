import json
import logging
from typing import TypedDict, Optional
from urllib.parse import urljoin

import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from gabbro.makan_client import SUCCESS_STATUS_CODES

accounts_settings = getattr(settings, "ACCOUNTS", {})
logger = logging.getLogger("common")


class UserDictType(TypedDict):
    email: Optional[str]
    phone: Optional[str]
    pk: Optional[int]


class SendSMSDictType(TypedDict):
    to: str
    message: str


class CreateUserDictType(TypedDict):
    email: Optional[str]
    phone: Optional[str]


class AccountsClient(object):
    def __init__(self, ssl_verify=True):
        self.verify = ssl_verify
        self.connection_error = None
        self._prep_session()
        self.api_url = urljoin(
            accounts_settings.get("INTERNAL_BASE_URL"), "internal/v1/users/"
        )
        self._prep_session()

    def _prep_session(self):
        self.session = requests.session()
        self.session.headers.update(
            {
                "content-type": "application/json;charset=UTF-8",
            }
        )

    def _close_session(self):
        self.session.close()
        self.session = None

    def _reset_session(self):
        self._close_session()
        self._prep_session()

    def _make_request(self, method: str, *args, **kwargs):
        req_method = {"POST": self.session.post, "GET": self.session.get}[method]
        res = None

        if self.connection_error is not None:
            self.connection_error = None
            self._reset_session()

        try:
            res, _ = req_method(*args, **kwargs), None
        except requests.exceptions.SSLError:
            if self.verify:
                self.verify = False
                return self._make_request(req_method, *args, **kwargs)
        except requests.exceptions.RequestException as err:
            self.connection_error = (
                f"{err.__class__.__name__}: Error connecting to Accounts!"
            )
            return None, self.connection_error

        if res.status_code in SUCCESS_STATUS_CODES:
            return res, None
        return None, res.text

    def internal_create_user(self, data: CreateUserDictType):
        url = urljoin(self.api_url, "inactive/")
        res, err = self._make_request("POST", url, data=json.dumps(data))
        if err:
            raise ValidationError({"errors": err})
        return res.json()

    def internal_send_sms(self, data: SendSMSDictType):
        url = urljoin(self.api_url, "send-sms/")
        res, err = self._make_request("POST", url, data=json.dumps(data))
        if err:
            raise ValidationError({"errors": err})
        return res.json(), None

    def internal_retrieve_user(self, data: UserDictType):
        res, err = self._make_request("GET", self.api_url, params=data)
        if err:
            return None, err
        user = res.json()
        return user, None


accounts = AccountsClient()
