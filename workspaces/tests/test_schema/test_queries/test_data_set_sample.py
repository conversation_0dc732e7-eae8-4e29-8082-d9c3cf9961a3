import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.handlers.dataset_files import DatasetFilesLoader
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestDatasetSample(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.csv"),
        )
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            organization=self.organization,
            layer_data={
                "color": "#42FAC2",
                "title": "test dataset",
                "created": "2024-12-06T19:07:40",
                "read_only": False,
                "description": "ny desc",
                "json_schema": {},
                "web_ui_json_schema": None,
                "location_field_mapping": {
                    "coordinate_type": "point",
                    "lang_lat_column": "",
                    "latitude_column": "X",
                    "longitude_column": "Y",
                    "lat_lon_column_num": "two_column",
                },
            },
        )
        self.query = """
        query MyQuery($datasetRequestId: Int!, $orgId: Int!) {
          datasetSampleData(datasetRequestId: $datasetRequestId, orgId: $orgId) {
            data {
              data
              column
            }
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["datasetSampleData"]
        data = data["data"]
        loader = DatasetFilesLoader(file=self.dataset_request.dataset.file)
        sample_data = loader.get_sample_data(
            rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER
        )
        self.assertListEqual(data, sample_data)

    def test_query_with_sample_data(self):
        sample_data = [{"column": "X", "data": [{"test": "test"}]}]
        self.dataset.meta_data["sample_data"] = sample_data
        self.dataset.save(update_fields=["meta_data"])
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["datasetSampleData"]
        data = data["data"]
        self.assertListEqual(data, sample_data)

    def test_query_with_superuser(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.super_user_auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["datasetSampleData"]
        data = data["data"]
        loader = DatasetFilesLoader(file=self.dataset_request.dataset.file)
        sample_data = loader.get_sample_data(
            rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER
        )
        self.assertListEqual(data, sample_data)

    def test_query_with_invalid_dataset(self):
        user = UserFactory(is_superuser=False)
        dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=user,
            organization=self.organization,
        )
        self.variables["datasetRequestId"] = dataset_request.id
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "datasetRequestId": _("Invalid dataset_request_id: %(id)s")
                % {"id": self.variables["datasetRequestId"]}
            },
        )
