{"$schema": "http://json-schema.org/draft-07/schema#", "title": "GeoCore Database", "type": "object", "description": "The main GeoCore database that contains all needed tables", "properties": {"layers_layer": {"type": "object", "title": "layers table", "description": "the table that stores the layers data", "properties": {"id": {"type": "integer", "title": "the primary key of the table"}, "key": {"type": "string", "title": "the slug of the layer which make the layer unique"}, "created": {"type": "string", "title": "the row creation datetime", "description": "تاريخ التسجيل"}, "modified": {"type": "string", "title": "the row modification datetime", "description": "تاريخ التعديل"}, "title": {"type": "string", "title": "layer title", "description": "العنوان"}, "description": {"type": "string", "title": "layer description", "description": "التفاصيل"}, "color": {"type": "string", "title": "the color of the layer"}, "read_only": {"type": "boolean", "title": "Is the layer's records editable or not"}, "json_schema": {"type": "object", "title": "the json schema of the layer's records' form_data"}, "dataset_id": {"type": "integer", "title": "the foreign key of the workspaces_dataset table", "description": "each layer will has a specific dataset"}, "workspace_id": {"type": "integer", "title": "the foreign key of the workspaces_workspace table", "description": "each layer will belong to a specific workspace"}, "status": {"$ref": "#/definitions/layer_statuses"}}}, "layers_record": {"type": "object", "title": "the table that will store the data of each polygon in the layer", "description": "جدول السجلات الجغرافية الذي يحتوي علي بيانات وخصائص السجل الجغرافي", "properties": {"id": {"type": "integer", "title": "the primary key of the table"}, "layer_id": {"type": "integer", "title": "the foreign key of the layers_layer table", "description": "Each layers_record will belong to a specific layer"}, "created": {"type": "string", "title": "تاريخ التسجيل"}, "modified": {"type": "string", "title": "تاريخ التعديل"}, "source_properties": {"type": "object", "title": "JSON column that contains all data of the record", "properties": {"city": {"type": "string"}, "name": {"type": "string"}, "poi_type": {"$ref": "#/definitions/poi_type_choices"}, "point_id": {"type": "integer"}, "parcel_id": {"type": ["number", "string"]}, "confidence": {"$ref": "#/definitions/confidence"}, "location_type": {"$ref": "#/definitions/location_type"}}}, "map_data": {"type": "object", "title": "JSON column that contains some data of the record"}, "geometry": {"type": "object", "title": "postgres geometry field", "description": "it's a postgres geometry field which contains a polygon"}}}, "users_user": {"type": "object", "title": "users table", "description": "مستخدمون النظام", "properties": {"id": {"type": "integer", "title": "the primary key of the table"}, "email": {"type": "string", "title": "البريد الالكتروني"}, "first_name": {"type": "string", "title": "الاسم الاول"}, "last_name": {"type": "string", "title": "اسم العائلة"}, "created_at": {"type": "string", "title": "تاريخ انضمام المستخدم"}}}, "workspaces_dataset": {"type": "object", "title": "the Dataset table", "properties": {"id": {"type": "integer", "title": "the primary key of the table"}, "file": {"type": "string", "title": "the URL of the dataset file"}, "created": {"type": "string", "title": "تاريخ التسجيل"}, "modified": {"type": "string", "title": "تاريخ التعديل"}, "meta_data": {"type": "object", "title": "some extra data about the dataset file"}}}, "workspaces_workspace": {"type": "object", "title": "the Workspace table", "properties": {"id": {"type": "integer", "title": "the primary key of the table"}, "name": {"type": "string", "title": "the name of the workspace"}, "description": {"type": "string", "title": "the description of the workspace"}, "owner_id": {"type": "integer", "title": "the foreign key of the owner user", "description": "each workspace has its own owner user"}, "created": {"type": "string", "title": "تاريخ التسجيل"}, "modified": {"type": "string", "title": "تاريخ التعديل"}}}}, "definitions": {"layer_statuses": {"type": "string", "enum": ["in_progress", "published", "unpublished"]}, "poi_type_choices": {"type": "string", "enum": ["BRT_station", "airport", "bus_station", "commercial", "cultural", "government", "historical", "metro_station", "mosques", "parks", "schools", "security", "sport"]}, "location_type": {"type": "string", "enum": ["building", "edge", "point"]}, "confidence": {"type": "string", "enum": ["High", "Low", "Medium"]}}}