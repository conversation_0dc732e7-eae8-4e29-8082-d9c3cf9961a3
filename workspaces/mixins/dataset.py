import logging
import os
from typing import List

from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest, NotFound
from gabbro.layers_engine.enums import LayerStatus

from common.handlers.dataset_files import DatasetFilesLoader
from common.utils import (
    EDAReportGenerator,
    EDA_REPORT_DIR,
    StorageBackend,
    convert_shapely_geometries_to_geojson,
    ALLOWED_DATASET_EXTENSIONS,
)
from workspaces.models import Dataset

logger = logging.getLogger("workspaces")
MEDIA_URL = getattr(settings, "MEDIA_URL")
storage = StorageBackend()


class DatasetMixin:
    @staticmethod
    def validate_dataset_file(dataset_file: str):
        extension = dataset_file.split(".")[-1]
        if extension not in ALLOWED_DATASET_EXTENSIONS:
            raise BadRequest(
                reason={
                    "dataset_file": _(
                        "Dataset file extension must be on of %(extension)s"
                    )
                    % {"extension": ", ".join(ALLOWED_DATASET_EXTENSIONS)}
                }
            )

    @staticmethod
    def validate_if_map_data_columns_included_in_dataset_sample(
        dataset: Dataset, map_data_columns: List[str]
    ):
        sample_data_columns = dataset.meta_data.get("columns")
        if not all([c in sample_data_columns for c in map_data_columns]):
            raise BadRequest(
                reason={
                    "error": _("columns %(columns)s not included in the dataset")
                    % {"columns": map_data_columns}
                }
            )

    @staticmethod
    def validate_and_get_dataset(dataset_id: int) -> Dataset:
        dataset = Dataset.objects.filter(id=dataset_id).first()
        if not dataset:
            raise NotFound(
                reason={
                    "dataset_id": _("Dataset with %(dataset_id)s not found")
                    % {"dataset_id": dataset_id}
                }
            )
        return dataset

    @staticmethod
    def create_dataset(title: str, file: str, **kwargs) -> Dataset:
        return Dataset.objects.create(title=title, file=file, **kwargs)

    @staticmethod
    def process_and_store_dataset_metadata(dataset: Dataset, title: str):
        # Loading data from dataset file
        loader = DatasetFilesLoader(file=dataset.file)
        sample_data = loader.get_sample_data(
            rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER
        )
        dataset.set_metadata({"columns": loader.sample_df.columns.tolist()})
        serialized_sample_data = convert_shapely_geometries_to_geojson(sample_data)
        dataset.set_metadata({"sample_data": serialized_sample_data})
        logger.debug(
            f"[process_and_store_dataset_metadata] sample data saved into dataset: {dataset}"
        )

        # Generating EDA Report
        handler = DatasetEDAHandler(dataset=dataset)
        handler.handle(title=title)
        logger.debug(
            f"[process_and_store_dataset_metadata] EDA report generated for {title}"
        )


class DatasetEDAHandler:
    def __init__(self, dataset: Dataset):
        self.dataset = dataset
        self.eda_generator = EDAReportGenerator(identifier=self.dataset.pk)
        self.debug = lambda method, message: logger.debug(
            f"[{self.__class__}][{method}] {message}"
        )

    def handle(self, title: str):
        try:
            self.set_dataset_status(LayerStatus.REPORT_GENERATE_PENDING.value)
            eda_reports = self.eda_generator.generate_eda_reports_from_file(
                file=self.dataset.file, title=title
            )
            self.delete_older_report()
            self.debug("handle", "older report deleted")
            self.update_dataset_metadata(eda_reports)
            self.dataset.create_eda_reports(self.dataset.meta_data["eda_reports"])
        except Exception as e:
            self.debug("handle", f"exception: {e}")
            self.set_dataset_status(
                status=LayerStatus.PUBLISH_ERROR.value, errors=f"Unexpected Error! {e}"
            )
            self.dataset.set_metadata({"columns": self.eda_generator.columns})

    def delete_older_report(self):
        hash_code = self.dataset.meta_data.get("eda_reports", {}).get("hash", None)
        hash_path = os.path.join(
            EDA_REPORT_DIR, str(self.dataset.pk), f"{hash_code}.html"
        )
        if hash_code and storage.validate_file_path_is_exists(hash_path):
            storage.delete(hash_path)

    def update_dataset_metadata(self, eda_reports) -> None:
        """
        Updates dataset metadata with the hash of the newly generated report.
        """
        eda_reports_data = list()
        for eda_report in eda_reports:
            full_report_url = self._get_full_report_url(eda_report["report_path"])
            eda_reports_data.append(
                {
                    "status": LayerStatus.PUBLISHED.value,
                    "hash": eda_report["hash"],
                    "report_path": full_report_url,
                    "created_at": timezone.now().isoformat(),
                    "errors": None,
                    "source": eda_report["source"],
                }
            )
        self.dataset.set_metadata(
            {
                "columns": self.eda_generator.columns,
                "eda_reports": eda_reports_data,
            }
        )

    @staticmethod
    def _get_full_report_url(report_path):
        if settings.STORAGE_TYPE != "LOCAL":
            return os.path.join(MEDIA_URL, report_path)
        return report_path

    def set_dataset_status(self, status: str, errors: str = None) -> None:
        """
        Updates dataset metadata with the current status and any errors.
        """
        self.dataset.set_metadata(
            {
                "eda_reports": {
                    **self.dataset.meta_data.get("eda_report", {}),
                    "status": status,
                    "errors": errors,
                }
            }
        )


def generate_eda_report_from_dataset(dataset_id: int, title: str):
    # Generating EDA Report
    dataset = Dataset.objects.get(id=dataset_id)
    handler = DatasetEDAHandler(dataset=dataset)
    handler.handle(title=title)
    logger.debug(f"[generate_eda_report_from_dataset] EDA report generated for {title}")


def loading_meta_data_from_dataset(dataset: Dataset):
    # Loading data from dataset file
    loader = DatasetFilesLoader(file=dataset.file)
    sample_data = loader.get_sample_data(
        rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER
    )
    dataset.set_metadata({"columns": loader.sample_df.columns.tolist()})
    serialized_sample_data = convert_shapely_geometries_to_geojson(sample_data)
    dataset.set_metadata({"sample_data": serialized_sample_data})
    logger.debug(
        f"[generate_eda_report_from_dataset] sample data saved into dataset: {dataset}"
    )
