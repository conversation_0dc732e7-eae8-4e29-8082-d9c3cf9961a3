import json
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

from django.conf import settings
from django.core.files.storage import default_storage
from django.core.management import BaseCommand, CommandError

from common.utils import slice_list_to_chunks, calculate_boundaries_postgis
from layers.management.commands.load_tataba_layers_from_geopackage import (
    geopackage_to_geojson_generator,
    create_records_from_features,
    get_features_count_from_geopackage,
)
from layers.models import Layer, Record
from organizations.models import Organization
from users.models import User
from workspaces.models import Workspace, Dataset
from workspaces.strategies import CreateUploadFileRequestStrategy

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("--organizations_path", type=str, required=True)
        parser.add_argument("--layers_path", type=str, required=True)
        parser.add_argument("--datasets_path", type=str, required=True)
        parser.add_argument("--organization_id", type=int, required=False)
        parser.add_argument("--owner_id", type=int, required=False)
        parser.add_argument(
            "--organization_ids",
            type=lambda s: [int(item.strip()) for item in s.split(",")],
            help="Export Specific Organizations",
        )
        parser.add_argument(
            "--analyze",
            action="store_true",
            help="If provided, analyze data before exporting.",
        )

    def handle(self, *args, **options):
        start = time.time()
        organizations_path = options["organizations_path"]
        datasets_path = options["datasets_path"]
        layers_path = options["layers_path"]
        organization_ids = options.get("organization_ids")
        analyze = options.get("analyze")

        if not default_storage.exists(organizations_path):
            raise CommandError(
                f"this organizations_path not Found {organizations_path}"
            )
        if not default_storage.exists(datasets_path):
            raise CommandError(f"this datasets_path not Found {datasets_path}")
        if not default_storage.exists(layers_path):
            raise CommandError(f"this layers_path not Found {layers_path}")

        if analyze:
            logger.debug("Analyzing...")
            organizations_data = analyze_organizations_data(
                organizations_path, datasets_path
            )
            logger.debug(
                f"Organization Data: {json.dumps(organizations_data, indent=2)}"
            )
            return

        organization = Organization.objects.filter(
            id=options.get("organization_id")
        ).first()
        if not organization:
            raise CommandError("The organization_id must be a valid id")

        owner = User.objects.filter(id=options.get("owner_id")).first()
        if not owner:
            raise CommandError("The owner_id must be a valid id")

        load_old_tataba_data(
            organizations_path=organizations_path,
            datasets_path=datasets_path,
            layers_path=layers_path,
            organization=organization,
            owner=owner,
            organization_ids=organization_ids,
        )
        end = time.time()
        logger.debug(f"All Layers Loaded in {end-start} seconds")


def analyze_organizations_data(organizations_path: str, datasets_path: str):
    organizations_data = list()
    organizations_file = default_storage.open(organizations_path)
    organizations = json.load(organizations_file)
    for organization in organizations:
        layer_ids = default_storage.listdir(f"{datasets_path}/{organization['pk']}")[0]
        layers_data = list()
        total_records_count = 0
        for layer_id in layer_ids:
            dataset_file_path = default_storage.listdir(
                f"{datasets_path}/{organization['pk']}/{layer_id}/"
            )[1][0]
            dataset_file = default_storage.open(
                f"{datasets_path}/{organization['pk']}/{layer_id}/{dataset_file_path}"
            )
            records_count = get_features_count_from_geopackage(dataset_file)
            total_records_count += records_count
            layers_data.append(
                dict(
                    layer_id=layer_id,
                    records_count=records_count,
                    dataset_file_path=dataset_file_path,
                )
            )
        organizations_data.append(
            dict(
                id=organization["pk"],
                data=dict(
                    layers_count=len(layer_ids),
                    records_count=total_records_count,
                    layers_data=layers_data,
                ),
            )
        )
    return organizations_data


def load_old_tataba_data(
    organizations_path: str,
    datasets_path: str,
    layers_path: str,
    organization: Organization,
    owner: User,
    organization_ids: list[int] = None,
):
    workspaces = load_workspaces_from_old_tataba_organizations(
        path=organizations_path,
        organization=organization,
        owner=owner,
        organization_ids=organization_ids,
    )
    datasets = load_datasets_from_geopackages(
        path=datasets_path, workspaces=workspaces, organization_ids=organization_ids
    )
    layers = load_layers_from_old_tataba_layers(
        path=layers_path,
        workspaces=workspaces,
        datasets=datasets,
        organization=organization,
    )
    load_layers_records_with_parallel_processing(
        layers=layers, organization=organization
    )
    logger.debug("Loading records from old tataba layers finished")
    logger.debug("Exiting ......")


def load_layers_records_with_parallel_processing(
    layers, organization: Organization, replace=False, batch_size=5000
):
    logger.debug("Loading records from old tataba layers")
    logger.debug("Starting ......")
    # Use ThreadPoolExecutor for parallel processing
    with ThreadPoolExecutor(max_workers=8) as executor:
        future_to_layer = {
            executor.submit(
                process_layer, layer, organization, replace, batch_size
            ): layer
            for layer in layers
        }
        for future in as_completed(future_to_layer):
            future.result()
            logger.debug("=" * 200)
    logger.debug("All Layers Imported Successfully")


def process_layer(
    layer: Layer, organization: Organization, replace=False, batch_size=5000
):
    """Process a single layer."""
    try:
        if replace:
            layer.records.all().delete()
        load_records_from_old_tataba_layers(layer=layer, batch_size=batch_size)
        dataset = layer.dataset
        inject_summary_fields_into_layer(layer=layer, dataset=dataset)
        calculate_layer_boundaries(layer=layer)
        layer.save(update_fields=["data", "boundaries", "modified"])
        dataset.save(update_fields=["meta_data", "modified"])
        logger.debug(f"Layer processed: {layer.id}")
        increase_organization_layers(organization=organization, layer=layer)
        layer.workspace.increase_layers_count()
        layer.workspace.increase_records_count(layer.records.count())
        return layer
    except Exception as e:
        logger.error(f"Error processing layer {layer.id}: {e}")
        return None


def increase_organization_layers(organization: Organization, layer: Layer):
    loaded_layers_ids = organization.workspaces_data["loaded_layers_ids"]
    loaded_layers_count = organization.workspaces_data["loaded_layers_count"]
    loaded_layers_ids.append(layer.id)
    loaded_layers_count += 1
    unloaded_layers_ids = organization.workspaces_data["unloaded_layers_ids"]
    unloaded_layers_count = organization.workspaces_data["unloaded_layers_count"]
    unloaded_layers_ids.remove(layer.id)
    unloaded_layers_count -= 1
    organization.set_workspaces_data(
        dict(
            loaded_layers_ids=loaded_layers_ids,
            unloaded_layers_ids=unloaded_layers_ids,
            loaded_layers_count=loaded_layers_count,
            unloaded_layers_count=unloaded_layers_count,
        )
    )


def load_workspaces_from_old_tataba_organizations(
    path: str,
    organization: Organization,
    owner: User,
    organization_ids: list[int] = None,
) -> dict[int, Workspace]:
    logger.debug("=" * 150)
    logger.debug("Loading workspaces from old tataba organizations")
    logger.debug("Starting ......")
    organizations_file = default_storage.open(path)
    organizations = json.load(organizations_file)
    workspaces = dict()
    for org in organizations:
        if organization_ids and int(org["pk"]) not in organization_ids:
            continue
        workspaces[int(org["pk"])] = Workspace(
            organization_id=organization.id,
            name=f"{org['pk']}_{org['fields']['settings']['name']}",
            owner_id=owner.id,
            thumbnail=org["fields"]["settings"]["full_logo"],
        )
    Workspace.objects.bulk_create(workspaces.values())
    logger.debug(f"All workspaces created [{workspaces}]")
    logger.debug("Exiting ......")
    return workspaces


def load_datasets_from_geopackages(
    path: str, workspaces: dict[int, Workspace], organization_ids: list[int] = None
) -> dict[int, Dataset]:
    logger.debug("=" * 150)
    logger.debug("Loading datasets from geopackages")
    logger.debug("Starting ......")
    org_ids = default_storage.listdir(path)[0]
    datasets = dict()
    for org_id in org_ids:
        if organization_ids and int(org_id) not in organization_ids:
            continue
        layer_ids = default_storage.listdir(f"{path}/{org_id}")[0]
        logger.debug(f"org_id: {org_id} layer_ids count: {len(layer_ids)}")
        for layer_id in layer_ids:
            dataset_file_path = default_storage.listdir(f"{path}/{org_id}/{layer_id}/")[
                1
            ][0]
            dataset_file = default_storage.open(
                f"{path}/{org_id}/{layer_id}/{dataset_file_path}"
            )
            datasets[int(layer_id)] = Dataset(
                file=f"{dataset_file}", workspace=workspaces[int(org_id)]
            )
    Dataset.objects.bulk_create(datasets.values())
    logger.debug(f"All datasets created {len(datasets)}")
    logger.debug("Exiting ......")
    return datasets


def load_layers_from_old_tataba_layers(
    path: str,
    workspaces: dict[int, Workspace],
    datasets: dict[int, Dataset],
    organization: Organization,
):
    logger.debug("=" * 150)
    logger.debug("Loading layers from old tataba layers")
    logger.debug("Starting ......")
    layers_file = default_storage.open(path)
    layers = json.load(layers_file)
    layers_objs = list()
    updated_datasets = list()
    for layer in layers:
        organization_id = layer["fields"]["organization"]
        workspace = workspaces.get(organization_id)
        if not workspace:
            continue
        dataset = datasets.get(int(layer["pk"]))
        if not dataset:
            logger.debug(
                f"dataset not found for layer: {layer['pk']} organization: {organization_id}"
            )
            continue
        layers_objs.append(
            Layer(
                workspace=workspace,
                dataset=dataset,
                key=layer["fields"]["name"],
                title=layer["fields"]["title"],
                description=layer["fields"]["description"],
                color=layer["fields"]["geometry_color"],
                json_schema=layer["fields"]["json_schema"],
                web_ui_json_schema=layer["fields"]["web_ui_json_schema"],
            )
        )
        dataset.title = layer["fields"]["title"]
        dataset.meta_data = {
            **dataset.meta_data,
            **{
                "json_schema": layer["fields"]["json_schema"],
                "web_ui_json_schema": layer["fields"]["web_ui_json_schema"],
            },
        }
        updated_datasets.append(dataset)
    Dataset.objects.bulk_update(
        objs=updated_datasets, fields=["meta_data", "title", "modified"]
    )
    logger.debug(f"Datasets updated {len(updated_datasets)}")
    layers_objs = Layer.objects.bulk_create(layers_objs)
    organization.set_workspaces_data(
        dict(
            unloaded_layers_ids=[layer.id for layer in layers_objs],
            unloaded_layers_count=len(layers_objs),
        )
    )
    logger.debug(f"organization workspaces_data = {organization.workspaces_data}")
    logger.debug(f"All layers created {len(layers_objs)}")
    logger.debug("Exiting ......")
    return layers_objs


def load_records_from_old_tataba_layers(layer: Layer, batch_size=5000):
    file_object = default_storage.open(layer.dataset.file)
    features_list = geopackage_to_geojson_generator(
        file_object=file_object, batch_size=batch_size
    )
    create_records_from_features(features_list=features_list, layer=layer)
    logger.debug(f"Finished of loading the file: {file_object}")


def fill_datasets_and_layers_with_geocore_data(workspaces, skip_if_exist=False):
    logger.debug("Filling datasets with geocore data")
    logger.debug("Starting ......")
    for workspace in workspaces:
        workspace: Workspace = workspace
        logger.debug(f"Updating Workspace: {workspace.id}")
        workspace.thumbnail = f"{settings.STATIC_URL}workspaces/default_thumbnail.png"
        workspace.layers_sorted_ids = list(
            workspace.layers.order_by("-created").values_list("id", flat=True)
        )
        layers = workspace.layers.select_related("dataset")
        if skip_if_exist:
            layers = layers.filter(eda_reports__isnull=True)
            logger.debug(f"Updating {len(layers)} layers without EDA report")
        else:
            logger.debug(f"Updating {len(layers)} layers with EDA report")
        for layer in layers:
            strategy = CreateUploadFileRequestStrategy()
            thread = threading.Thread(
                target=strategy.process_and_store_dataset_metadata,
                args=(layer.dataset, layer.title),
            )
            thread.start()
            thread.join()
            layer.refresh_from_db()
            if eda_reports := layer.dataset.meta_data["eda_reports"]:
                layer.dataset.create_eda_reports(eda_reports)
    Workspace.objects.bulk_update(
        objs=workspaces, fields=["thumbnail", "layers_sorted_ids"]
    )
    logger.debug("Datasets filled with geocore data")
    logger.debug("Exiting ....")


def calculate_layer_boundaries(layer: Layer):
    layer.boundaries = calculate_boundaries_postgis(
        records=layer.records.only("geometry")
    )


def inject_summary_fields_into_layer(layer: Layer, dataset: Dataset):
    if record := layer.records.filter(map_data__isnull=False).first():
        summary_fields = list(record.map_data.keys())[:6]
        layer.data["summary_fields"] = summary_fields
        dataset.meta_data["summary_fields"] = summary_fields


def convert_string_to_dict_in_records():
    records = list(
        Record.objects.raw(
            """
                SELECT id, map_data FROM layers_record
                WHERE jsonb_typeof(map_data) = 'string'
            """
        )
    )
    for chunk in slice_list_to_chunks(records):
        for record in chunk:
            properties = record.source_properties
            properties = json.loads(
                properties.replace("'", '"')
                .replace("None", "null")
                .replace("True", "true")
                .replace("False", "false")
            )
            record.source_properties = properties
            record.map_data = properties
            record.data = properties
        Record.objects.bulk_update(chunk, ["source_properties", "map_data", "data"])
        logger.debug(f"Records Updated: {len(chunk)}")
    logger.debug("Finished!")
    logger.debug("Exiting ....")


def get_nested_data_from_record_properties(layers):
    for layer in layers:
        records = list(layer.records.filter(map_data__isnull=False))
        for chunk in slice_list_to_chunks(records):
            records_to_update = list()
            for record in chunk:
                if (
                    "id" in record.source_properties
                    and "data" in record.source_properties
                ):
                    logger.debug(f"record_id = {record.id}")
                    properties = record.source_properties["data"]
                    logger.debug(f"properties: {properties}")
                    record.source_properties = properties
                    record.map_data = properties
                    record.data = properties
                    records_to_update.append(record)
            if records_to_update:
                Record.objects.bulk_update(
                    records_to_update, ["source_properties", "map_data", "data"]
                )
                logger.debug(f"Records Updated: {len(chunk)}")
    logger.debug("Finished!!!")
