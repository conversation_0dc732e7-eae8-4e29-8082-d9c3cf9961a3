# Generated by Django 3.2.25 on 2025-07-06 13:56

from django.db import migrations

from layers.models.typed_dict import LayerSLD


def migrate_layer_sld_data_into_sld_model(apps, schema_editor):
    Layer = apps.get_model("layers", "Layer")
    SLD = apps.get_model("layers", "SLD")
    layers = Layer.objects.filter(slds__isnull=True).only("id", "data")
    for layer in layers:
        slds = [
            SLD(
                title=layer.key,
                feature_style=LayerSLD(
                    color=layer.color,
                    opacity=layer.opacity,
                    stroke_color=layer.color,
                    stroke_width=1,
                    stroke_opacity=layer.opacity,
                ),
            )
        ]
        if layer.heatmap_data:
            slds.append(
                SLD(
                    title=f"{layer.key}_heat_map",
                    sld_type="heatmap",
                    feature_style=layer.heatmap_data,
                )
            )
        SLD.objects.bulk_create(slds)
        layer.slds.add(*slds)


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0021_auto_20250706_1354"),
    ]

    operations = [
        migrations.RunPython(
            migrate_layer_sld_data_into_sld_model, migrations.RunPython.noop
        )
    ]
