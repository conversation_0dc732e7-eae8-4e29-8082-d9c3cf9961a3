import graphene
from gabbro.graphene.exceptions import BadRequest

from common.utils import authentication_required, organization_required
from organizations.schema.input_object_types import OrganizationInputType
from organizations.schema.object_types import OrganizationType
from organizations.serializers import OrganizationSerializer


class UpdateOrganization(graphene.Mutation):
    organization = graphene.Field(OrganizationType)

    class Input:
        data_input = OrganizationInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        organization = info.context.organization
        serializer = OrganizationSerializer(instance=organization, data=data_input)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        organization = serializer.save()
        return UpdateOrganization(organization=organization)


class Mutation(graphene.ObjectType):
    update_organization = UpdateOrganization.Field()
