# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-05 14:19+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: app/urls.py:30
msgid "Geo Core Admin"
msgstr "إدارة نواة جيو"

#: app/urls.py:31
msgid "Geo Core Admin Portal"
msgstr "بوابة إدارة نواة جيو"

#: app/urls.py:32
msgid "Welcome to Geo Core Admin"
msgstr "مرحبًا بك في إدارة نواة جيو"

#: chat_ai/models/conversation.py:10
msgid "UUID PK"
msgstr "UUID المفتاح الأساسي"

#: chat_ai/models/conversation.py:13
msgid "layers"
msgstr "الطبقات"

#: chat_ai/models/conversation.py:19
msgid "user"
msgstr "المستخدم"

#: chat_ai/models/conversation.py:23
msgid "Conversation"
msgstr "المحادثة"

#: chat_ai/models/conversation.py:24
msgid "Conversations"
msgstr "المحادثات"

#: chat_ai/models/message.py:12
msgid "conversation"
msgstr "المحادثة"

#: chat_ai/models/message.py:15
msgid "message"
msgstr "الرسالة"

#: chat_ai/models/message.py:16
msgid "contains user input, GPT output, and SQL Query"
msgstr "يتضمن إدخال المستخدم، مخرجات GPT، واستعلام SQL"

#: chat_ai/models/message.py:20
msgid "Message"
msgstr "الرسالة"

#: chat_ai/models/message.py:21
msgid "Messages"
msgstr "الرسائل"
#: chat_ai/schema/utils.py:19 layers/schema/query.py:57
#: workspaces/schema/query.py:273
msgid "Invalid workspace_id"
msgstr "معرف مساحة العمل غير صالح"

#: chat_ai/schema/utils.py:36
msgid "No layers found with the provided IDs"
msgstr "لم يتم العثور على أي طبقات باستخدام المعرفات المقدمة"

#: common/apps.py:8
msgid "Common"
msgstr "عام"

#: common/utils/eda_generator.py:68
#, python-format
msgid "File does not exist! %(file_path)s"
msgstr "الملف غير موجود! %(file_path)s"

#: common/utils/geometry.py:21
msgid "Invalid geometry format"
msgstr "تنسيق الهندسة غير صالح"

#: common/utils/geometry.py:25
msgid "Invalid geometry: Not a valid geometry object."
msgstr "الهندسة غير صالحة: ليست كائن هندسي صالح."

#: common/utils/graphene/decorators.py:20
msgid "Your account is deleted"
msgstr "تم حذف حسابك"

#: common/utils/graphene/decorators.py:22
msgid "Your account is inactive"
msgstr "حسابك غير نشط"

#: common/utils/graphene/decorators.py:41
#, python-format
msgid "Organization with id %(org_id)s not found"
msgstr "لم يتم العثور على المنظمة بالمعرف %(org_id)s"

#: common/utils/graphene/decorators.py:52 users/mixins/users.py:27
#, python-format
msgid "User %(user_id)s not in organization %(org_id)s"
msgstr "المستخدم %(user_id)s ليس في المنظمة %(org_id)s"

#: common/utils/graphene/decorators.py:67
#: common/utils/graphene/decorators.py:80 layers/permissions/record.py:15
msgid "Permission denied"
msgstr "تم رفض الإذن"

#: common/utils/graphene/query.py:57
msgid "Value provided is not an integer"
msgstr "القيمة المقدمة ليست عددًا صحيحًا"

#: common/utils/graphene/query.py:73
#, python-format
msgid "Value must be bigger than %(min_value)s"
msgstr "يجب أن تكون القيمة أكبر من %(min_value)s"

#: layers/admin/layer.py:49
#, python-format
msgid "Layers %(queryset)s are published"
msgstr "الطبقات %(queryset)s تم نشرها"

#: layers/admin/layer.py:54
msgid "Publish"
msgstr "نشر"

#: layers/admin/layer.py:55
msgid "Publish to Geoserver"
msgstr "نشر إلى Geoserver"

#: layers/models/layer.py:12
msgid "Published"
msgstr "منشور"

#: layers/models/layer.py:13
msgid "Unpublished"
msgstr "غير منشور"

#: layers/models/layer.py:23 workspaces/models/dataset.py:27
#: workspaces/models/request.py:52
msgid "Dataset"
msgstr "مجموعة البيانات"

#: layers/models/layer.py:29 workspaces/models/dataset.py:15
#: workspaces/models/workspace.py:66
msgid "Workspace"
msgstr "مساحة العمل"

#: layers/models/layer.py:32
msgid "Unique Form Key"
msgstr "مفتاح النموذج الفريد"

#: layers/models/layer.py:34
msgid "Layer Title"
msgstr "عنوان الطبقة"

#: layers/models/layer.py:36
msgid "Layer Description"
msgstr "وصف الطبقة"

#: layers/models/layer.py:41
msgid "Fill Color"
msgstr "لون التعبئة"

#: layers/models/layer.py:47
msgid "Layer Opacity"
msgstr "تعتيم الطبقة"

#: layers/models/layer.py:51
msgid "Read Only"
msgstr "للقراءة فقط"

#: layers/models/layer.py:52
msgid "Is this layer editable or not?"
msgstr "هل هذه الطبقة قابلة للتحرير أم لا؟"

#: layers/models/layer.py:59
msgid "Layer Status"
msgstr "حالة الطبقة"

#: layers/models/layer.py:62
msgid "Boundaries"
msgstr "الحدود"

#: layers/models/layer.py:65
msgid "Location Field Mapping"
msgstr "تعيين حقل الموقع"

#: layers/models/layer.py:73
msgid "JSON Schema"
msgstr "مخطط JSON"

#: layers/models/layer.py:74
msgid "Form and UI Schemas"
msgstr "مخططات النموذج وواجهة المستخدم"

#: layers/models/layer.py:79
msgid "Layer Web UI JsonSchema"
msgstr "واجهة الويب للطبقة مخطط JSON"

#: layers/models/layer.py:84 workspaces/models/request.py:76
msgid "Layer Data"
msgstr "بيانات الطبقة"

#: layers/models/layer.py:85
msgid "Layer extra data"
msgstr "بيانات إضافية للطبقة"

#: layers/models/layer.py:87
msgid "Heatmap Data"
msgstr "الخريطة الحرارية"

#: layers/models/layer.py:89
msgid "Records Last Modified"
msgstr "آخر تعديل للسجلات"

#: layers/models/layer.py:93 workspaces/models/request.py:60
msgid "Layer"
msgstr "طبقة"

#: layers/models/layer.py:94
msgid "Layers"
msgstr "الطبقات"

#: layers/models/record.py:14
msgid "Related Layer"
msgstr "الطبقة المرتبطة"

#: layers/models/record.py:19
msgid "Geometry Collection Record"
msgstr "سجل مجموعة الهندسة"

#: layers/models/record.py:24
msgid "Buffer Geometry"
msgstr "هندسة العازل"

#: layers/models/record.py:29
msgid "Source Properties"
msgstr "خصائص المصدر"

#: layers/models/record.py:34
msgid "Map Data"
msgstr "بيانات الخريطة"

#: layers/models/record.py:39
msgid "Order Data Dependency"
msgstr "اعتماد بيانات الطلب"

#: layers/models/record.py:44
msgid "Weight"
msgstr "الوزن"

#: layers/models/record.py:45
msgid "the weight values from the data field that affects on heatmap"
msgstr "قيم الوزن من حقل البيانات التي تؤثر على الخريطة الحرارية"

#: layers/models/record.py:49
msgid "Geometry Record"
msgstr "سجل الهندسة"

#: layers/models/record.py:50
msgid "Geometry Records"
msgstr "سجلات الهندسة"

#: layers/strategies/layer.py:203
msgid "No data found in the specified range."
msgstr "لم يتم العثور على بيانات في النطاق المحدد"

#: layers/validators/layer.py:42
#, python-format
msgid "columns %(columns)s not included in the layer json schema"
msgstr "الأعمدة %(columns)s غير مدمجة في مصفوفة البيانات"

#: layers/validators/record.py:51
#, python-format
msgid "Record with id %(record_id)s not found"
msgstr "لم يتم العثور على السجل بالمعرف %(record_id)s"

#: layers/views.py:18
msgid "Your are not authorized to view this page."
msgstr "ليس لديك إذن لعرض هذه الصفحة."

#: layers/views.py:27
#, python-format
msgid "Can't publish layer %(pk)s because the layer is already published."
msgstr "لا يمكن نشر الطبقة %(pk)s لأن الطبقة قد تم نشرها بالفعل."
#: layers/views.py:37
#, python-format
msgid "Layer %(layer)s is published"
msgstr "الطبقة %(layer)s تم نشرها"

#: organizations/apps.py:7 organizations/models.py:34
msgid "Organizations"
msgstr "المنظمات"

#: organizations/models.py:24
msgid "Roles"
msgstr "الأدوار"

#: organizations/models.py:28
msgid "Workspaces Data"
msgstr "بيانات المساحات العمل"

#: organizations/models.py:33 workspaces/models/request.py:44
#: workspaces/models/workspace.py:38
msgid "Organization"
msgstr "منظمة"

#: organizations/perms_constants.py:28
msgid "Can view user"
msgstr "يمكنه عرض المستخدم"

#: organizations/perms_constants.py:29
msgid "Can add user"
msgstr "يمكنه إضافة مستخدم"

#: organizations/perms_constants.py:30
msgid "Can change user"
msgstr "يمكنه تعديل المستخدم"

#: organizations/perms_constants.py:31
msgid "Can delete user"
msgstr "يمكنه حذف المستخدم"

#: organizations/perms_constants.py:33
msgid "Can view layer"
msgstr "يمكنه عرض الطبقة"

#: organizations/perms_constants.py:34
msgid "Can add layer"
msgstr "يمكنه إضافة طبقة"

#: organizations/perms_constants.py:35
msgid "Can delete layer"
msgstr "يمكنه حذف الطبقة"

#: organizations/perms_constants.py:36
msgid "Can edit layer"
msgstr "يمكنه تعديل الطبقة"

#: organizations/perms_constants.py:38
msgid "Can view record"
msgstr "يمكنه عرض السجل"

#: organizations/perms_constants.py:39
msgid "Can add record"
msgstr "يمكنه إضافة سجل"

#: organizations/perms_constants.py:40
msgid "Can delete record"
msgstr "يمكنه حذف السجل"

#: organizations/perms_constants.py:41
msgid "Can edit record"
msgstr "يمكنه تعديل السجل"

#: users/apps.py:7
msgid "Users"
msgstr "المستخدمون"

#: users/mixins/roles.py:14
#, python-format
msgid "Invalid role_id %(role_id)s."
msgstr "معرّف الدور %(role_id)s غير صالح."

#: users/mixins/users.py:15
#, python-format
msgid "User with id %(user_id)s not found"
msgstr "المستخدم الذي بالمعرف %(user_id)s غير موجود"

#: users/models.py:7
msgid "Active"
msgstr "نشط"

#: users/models.py:8
msgid "Inactive"
msgstr "غير نشط"

#: users/models.py:9
msgid "Waiting"
msgstr "قيد الانتظار"

#: users/models.py:10
msgid "Deleted"
msgstr "محذوف"

#: users/models.py:18
msgid "active status"
msgstr "حالة النشاط"

#: users/schema/mutations.py:148 users/schema/mutations.py:175
msgid "Invalid role_id"
msgstr "معرّف الدور غير صالح"

#: users/schema/query.py:91
msgid "No owner user found"
msgstr "لم يتم العثور على مستخدم مالك"

#: users/validators/roles.py:36
#, python-format
msgid "Invalid workspace %(workspace_id)s"
msgstr "مساحة العمل %(workspace_id)s غير صالحة"

#: users/validators/roles.py:46
#, python-format
msgid "Invalid users %(users_ids)s"
msgstr "معرّفات المستخدمين %(users_ids)s غير صالحة"

#: users/validators/roles.py:56
#, python-format
msgid "Invalid permissions %(permissions_ids)s"
msgstr "معرّفات الصلاحيات %(permissions_ids)s غير صالحة"

#: users/validators/user.py:19
msgid "User with this email already exists."
msgstr "يوجد مستخدم بالفعل بهذا البريد الإلكتروني."

#: users/validators/user.py:25
msgid "User with this phone already exists."
msgstr "يوجد مستخدم بالفعل بهذا الرقم."

#: users/validators/user.py:42
msgid "Invalid user_id"
msgstr "معرّف المستخدم غير صالح"

#: users/validators/user.py:50
msgid "User is waiting, you can't change activation status"
msgstr "المستخدم قيد الانتظار، لا يمكنك تغيير حالة التفعيل"

#: users/validators/user.py:56
msgid "Can't set activation to waiting"
msgstr "لا يمكن تعيين حالة التفعيل إلى قيد الانتظار"

#: workspaces/admin/eda_report.py:29
msgid "Report Link"
msgstr "رابط التقرير"

#: workspaces/apps.py:8 workspaces/models/workspace.py:67
msgid "Workspaces"
msgstr "المساحات العمل"

#: workspaces/mixins/dataset.py:18 workspaces/strategies/request.py:160
#: workspaces/strategies/request.py:178
#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:146
#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:388
#, python-format
msgid "columns %(columns)s not included in the dataset"
msgstr "الأعمدة %(columns)s غير مدمجة في مجموعة البيانات"

#: workspaces/mixins/dataset.py:29
#, python-format
msgid "Dataset with %(dataset_id)s not found"
msgstr "مجموعة البيانات التي بالمعرف %(dataset_id)s غير موجودة"

#: workspaces/mixins/request.py:34
#, python-format
msgid "You have %(request_type)s request in progress."
msgstr "لديك طلب من نوع %(request_type)s قيد المعالجة."

#: workspaces/mixins/request.py:66
#, python-format
msgid "Dataset with %(workspace_request_id)s not found"
msgstr "لم يتم العثور على مجموعة البيانات بالمعرّف %(workspace_request_id)s"

#: workspaces/mixins/request.py:80 workspaces/permissions/workspace.py:36
msgid "Permission denied, you do not have permission to this action."
msgstr "تم رفض الإذن، ليس لديك إذن للقيام بهذا الإجراء."

#: workspaces/mixins/workspace.py:17
#, python-format
msgid "Workspace with %(workspace_id)s not found"
msgstr "المساحة العمل التي بالمعرف %(workspace_id)s غير موجودة"

#: workspaces/models/dataset.py:19
msgid "Original Dataset File"
msgstr "ملف مجموعة البيانات الأصلية"

#: workspaces/models/dataset.py:22
msgid "Title"
msgstr "العنوان"

#: workspaces/models/dataset.py:24
msgid "Meta Data"
msgstr "البيانات الوصفية"

#: workspaces/models/dataset.py:28
msgid "Datasets"
msgstr "مجموعة البيانات"
#: workspaces/models/eda_report.py:7
msgid "YData"
msgstr "واي داتا"

#: workspaces/models/eda_report.py:8
msgid "SweetViz"
msgstr "سويت فيز"

#: workspaces/models/eda_report.py:16
msgid "layer"
msgstr "الطبقة"

#: workspaces/models/eda_report.py:18
msgid "hash code"
msgstr "كود التشفير"

#: workspaces/models/eda_report.py:19
msgid "file"
msgstr "الملف"

#: workspaces/models/eda_report.py:21
msgid "sources"
msgstr "المصادر"

#: workspaces/models/eda_report.py:25
msgid "EDA Report"
msgstr "تقرير التحليل الاستكشافي"

#: workspaces/models/eda_report.py:26
msgid "EDA Reports"
msgstr "تقارير التحليل الاستكشافي"

#: workspaces/models/request.py:20
msgid "In Progress"
msgstr "قيد التنفيذ"

#: workspaces/models/request.py:21
msgid "Finished"
msgstr "منجز"

#: workspaces/models/request.py:22
msgid "Cancelled"
msgstr "ملغى"

#: workspaces/models/request.py:26
msgid "Upload File"
msgstr "رفع ملف"

#: workspaces/models/request.py:27
msgid "Design Layer"
msgstr "تصميم طبقة"

#: workspaces/models/request.py:28
msgid "Connect Database"
msgstr "ربط قاعدة البيانات"

#: workspaces/models/request.py:36
msgid "Created by"
msgstr "تم الإنشاء بواسطة"

#: workspaces/models/request.py:67
msgid "Request Status"
msgstr "حالة الطلب"

#: workspaces/models/request.py:74
msgid "Request Type"
msgstr "نوع الطلب"

#: workspaces/models/request.py:80
msgid "Dataset Request"
msgstr "طلب مجموعة البيانات"

#: workspaces/models/request.py:81
msgid "Dataset Requests"
msgstr "طلبات مجموعة البيانات"

#: workspaces/models/workspace.py:30
msgid "Owner"
msgstr "المالك"

#: workspaces/models/workspace.py:40
msgid "Name"
msgstr "الاسم"

#: workspaces/models/workspace.py:42
msgid "Description"
msgstr "الوصف"

#: workspaces/models/workspace.py:45
msgid "Map Thumbnail"
msgstr "صورة مصغرة للخريطة"

#: workspaces/models/workspace.py:48
msgid "Last Visited"
msgstr "آخر زيارة"

#: workspaces/models/workspace.py:51
msgid "Layers Sorted IDs"
msgstr "معرفات الطبقات مرتبة"

#: workspaces/models/workspace.py:54
msgid "Layers Data"
msgstr "بيانات الطبقات"

#: workspaces/models/workspace.py:61
msgid "Workspace Type"
msgstr "نوع مساحة العمل"

#: workspaces/schema/query.py:149 workspaces/schema/query.py:187
#: workspaces/tests/test_schema/test_queries/test_data_set_sample.py:131
#: workspaces/tests/test_schema/test_queries/test_json_schemas.py:147
#, python-format
msgid "Invalid dataset_request_id: %(id)s"
msgstr "معرف طلب مجموعة البيانات غير صالح: %(id)s"

#: workspaces/schema/query.py:237
#, python-format
msgid "Workspace with id %(workspace_id)s not found"
msgstr "مساحة العمل بالمعرف %(workspace_id)s غير موجودة"

#: workspaces/schema/query.py:285
#, python-format
msgid "Layer with id %(layer_id)s not found"
msgstr "الطبقة بالمعرف %(layer_id)s غير موجودة"

#: workspaces/serializers/request.py:67
msgid ""
"lat_lon_column_num cannot be 'two_column' when coordinate_type is 'other'"
msgstr ""
"عدد عمود الإحداثيات (خط العرض والطول) لا يمكن أن يكون 'two_column' عندما "
"يكون نوع الإحداثيات 'آخر'"

#: workspaces/serializers/request.py:75 workspaces/serializers/request.py:82
#: workspaces/serializers/request.py:83
msgid "This field is required."
msgstr "هذا الحقل مطلوب."

#: workspaces/strategies/request.py:204
#, python-format
msgid "Invalid geometry columns %(columns)s"
msgstr "أعمدة الهندسة غير صالحة %(columns)s"

#: workspaces/tests/test_schema/test_mutations/test_cancel_dataset_request.py:56
#: workspaces/tests/test_schema/test_mutations/test_create_dataset_request.py:64
#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:81
#: workspaces/tests/test_schema/test_mutations/test_create_wokspace.py:152
#: workspaces/tests/test_schema/test_mutations/test_create_workspace.py:152
#: workspaces/tests/test_schema/test_mutations/test_update_json_schema.py:73
#: workspaces/tests/test_schema/test_queries/test_data_set_sample.py:72
#: workspaces/tests/test_schema/test_queries/test_dataset_request.py:64
#: workspaces/tests/test_schema/test_queries/test_json_schemas.py:79
#: workspaces/tests/test_schema/test_queries/test_workspaces.py:42
msgid "Unauthorized"
msgstr "غير مصرح"

#: workspaces/tests/test_schema/test_mutations/test_create_dataset_request.py:83
msgid "You have Upload File request in progress."
msgstr "لديك طلب رفع ملف قيد التنفيذ."

#: workspaces/tests/test_schema/test_mutations/test_create_dataset_request.py:96
#: workspaces/validators/request.py:58
#, python-format
msgid "Dataset file extension must be on of %(extension)s"
msgstr "امتداد ملف مجموعة البيانات يجب أن يكون من نوع %(extension)s"

#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:100
#: workspaces/tests/test_schema/test_mutations/test_create_wokspace.py:165
#: workspaces/tests/test_schema/test_mutations/test_create_workspace.py:165
#: workspaces/tests/test_schema/test_mutations/test_update_json_schema.py:86
#, python-format
msgid "Dataset with %(request_id)s not found"
msgstr "مجموعة البيانات التي بالمعرف %(request_id)s غير موجودة"

#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:223
msgid "Invalid geometry columns ['geom']"
msgstr "أعمدة الهندسة غير صالحة ['geom']"

#: workspaces/validators/request.py:130
#, python-format
msgid "%(error)s"
msgstr "%(error)s"

#: workspaces/validators/workspace.py:49
msgid "Layers sorted ids must be a subset of workspace layers"
msgstr "معرفات الطبقات المرتبة يجب أن تكون مجموعة فرعية من طبقات مساحة العمل"

#: workspaces/validators/workspace.py:96
#, python-format
msgid "Dataset with %(dataset_request_id)s not found"
msgstr "مجموعة البيانات بالمعرف %(dataset_request_id)s غير موجودة"
