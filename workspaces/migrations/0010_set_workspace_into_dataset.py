# Generated by Django 3.2.25 on 2024-12-30 13:45

from django.db import migrations


def set_workspace_into_dataset(apps, schema_editor):
    Dataset = apps.get_model("workspaces", "Dataset")
    Workspace = apps.get_model("workspaces", "Workspace")

    workspaces = Workspace.objects.all()
    for workspace in workspaces:
        datasets = Dataset.objects.filter(
            id__in=workspace.layers.filter(dataset__isnull=False)
            .values_list("dataset_id", flat=True)
            .distinct()
        )
        datasets.update(workspace=workspace)


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0009_dataset_workspace"),
    ]

    operations = [
        migrations.RunPython(set_workspace_into_dataset, migrations.RunPython.noop)
    ]
