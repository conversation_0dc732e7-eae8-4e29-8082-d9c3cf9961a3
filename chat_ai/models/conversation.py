from uuid import uuid4

from django.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class Conversation(TimeStampedModel):
    id = models.UUIDField(
        primary_key=True, default=uuid4, editable=False, verbose_name=_("UUID PK")
    )
    layers = models.ManyToManyField(
        "layers.Layer", related_name="conversations", verbose_name=_("layers")
    )
    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="conversations",
        verbose_name=_("user"),
    )

    class Meta:
        verbose_name = _("Conversation")
        verbose_name_plural = _("Conversations")

    def __str__(self):
        return f"pk: {self.pk}"
