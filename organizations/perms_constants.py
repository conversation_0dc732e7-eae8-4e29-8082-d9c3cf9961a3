from django.utils.translation import gettext_lazy as _

VIEW_USER = "view_user"
ADD_USER = "add_user"
CHANGE_USER = "change_user"
DELETE_USER = "delete_user"

VIEW_ORGANIZATION = "view_organization"
ADD_ORGANIZATION = "add_organization"
CHANGE_ORGANIZATION = "change_organization"
DELETE_ORGANIZATION = "delete_organization"

VIEW_LAYER = "view_layer"
ADD_LAYER = "add_layer"
DELETE_LAYER = "delete_layer"
EDIT_LAYER = "edit_layer"

VIEW_RECORD = "view_record"
ADD_RECORD = "add_record"
DELETE_RECORD = "delete_record"
EDIT_RECORD = "edit_record"

ADD_WORKSPACE = "add_workspace"
CHANGE_WORKSPACE = "change_workspace"
DELETE_WORKSPACE = "delete_workspace"
VIEW_WORKSPACE = "view_workspace"

ADD_ROLE = "add_role"
CHANGE_ROLE = "change_role"
DELETE_ROLE = "delete_role"
VIEW_ROLE = "view_role"

organization_model_permissions = (
    # User in a Group can View, Add, Change, or delete a user
    (VIEW_USER, _("Can view user")),
    (ADD_USER, _("Can add user")),
    (CHANGE_USER, _("Can change user")),
    (DELETE_USER, _("Can delete user")),
    # layer permissions
    (VIEW_LAYER, _("Can view layer")),
    (ADD_LAYER, _("Can add layer")),
    (DELETE_LAYER, _("Can delete layer")),
    (EDIT_LAYER, _("Can edit layer")),
    # layer records permissions
    (VIEW_RECORD, _("Can view record")),
    (ADD_RECORD, _("Can add record")),
    (DELETE_RECORD, _("Can delete record")),
    (EDIT_RECORD, _("Can edit record")),
    # workspace permissions
    (ADD_WORKSPACE, _("Can add workspace")),
    (CHANGE_WORKSPACE, _("Can change workspace")),
    (DELETE_WORKSPACE, _("Can delete workspace")),
    (VIEW_WORKSPACE, _("Can view workspace")),
    # role permissions
    (ADD_ROLE, _("Can add role")),
    (CHANGE_ROLE, _("Can change role")),
    (DELETE_ROLE, _("Can delete role")),
    (VIEW_ROLE, _("Can view role")),
)
