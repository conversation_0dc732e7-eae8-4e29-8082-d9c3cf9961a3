from django.db import models
from django.utils.translation import gettext_lazy as _
from gabbro.acl.models import Role
from gabbro.organizations.models import Organization as AbstractOrganization
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from .perms_constants import organization_model_permissions


def get_default_schema():
    return {}


def get_default_workspaces_data():
    return dict(
        loaded_layers_ids=[],
        unloaded_layers_ids=[],
        loaded_layers_count=0,
        unloaded_layers_count=0,
    )


class Organization(AbstractOrganization):
    roles = models.ManyToManyField(Role, blank=True, verbose_name=_("Roles"))
    workspaces_data = J<PERSON><PERSON>ield(
        blank=True,
        default=get_default_workspaces_data,
        verbose_name=_("Workspaces Data"),
    )

    class Meta:
        permissions = organization_model_permissions
        verbose_name = _("Organization")
        verbose_name_plural = _("Organizations")

    def set_workspaces_data(self, metadata: dict):
        workspaces_data = {**self.workspaces_data, **metadata}
        Organization.objects.filter(pk=self.pk).update(workspaces_data=workspaces_data)
        self.refresh_from_db()
