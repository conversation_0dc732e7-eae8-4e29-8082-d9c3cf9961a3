# Generated by Django 3.2.25 on 2024-11-10 08:25

import django.contrib.gis.db.models.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0003_layer_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="layer",
            name="boundaries",
            field=django.contrib.gis.db.models.fields.GeometryField(
                blank=True, null=True, srid=4326, verbose_name="Boundaries"
            ),
        ),
        migrations.AlterField(
            model_name="record",
            name="layer",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="records",
                to="layers.layer",
                verbose_name="Related Layer",
            ),
        ),
    ]
