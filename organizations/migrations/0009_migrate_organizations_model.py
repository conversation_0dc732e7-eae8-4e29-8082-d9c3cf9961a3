# Generated by Django 3.1.3 on 2022-03-19 12:47

import django.utils.timezone
import django_extensions.db.fields
import gabbro.organizations.models
from django.db import migrations, models, transaction
from django.db.utils import IntegrityError

from ._0009_constants import permissions_matrix

permissions_objects_matrix = dict()
roles_objects = dict()
rules_objects = list()


def create_organization_roles(apps):
    Permission = apps.get_model("auth", "Permission")
    ContentType = apps.get_model("contenttypes", "ContentType")
    Role = apps.get_model("acl", "Role")
    organization_content_type = ContentType.objects.get(model="organization")

    for role_title in permissions_matrix.keys():
        role_name = "-".join(role_title.split(" ")).lower()
        role, created = Role.objects.get_or_create(codename=role_name, title=role_title)
        permissions = Permission.objects.filter(
            codename__in=permissions_matrix[role_title],
            content_type=organization_content_type,
        )

        # cache permissions and roles objects
        permissions_objects_matrix[role_title] = permissions
        roles_objects[role_title] = role

        role.permissions.set(permissions)
        role.save()


def create_organization_rule_for_user(apps, organization, user, role_title):
    ContentType = apps.get_model("contenttypes", "ContentType")
    Rule = apps.get_model("acl", "Rule")

    organization_content_type = ContentType.objects.get(model="organization")
    role_object = roles_objects[role_title]

    rule, _ = Rule.objects.get_or_create(
        content_type=organization_content_type,
        object_id=organization.pk,
        role=role_object,
        user=user,
    )


def assign_rules_permissions(apps):
    ContentType = apps.get_model("contenttypes", "ContentType")
    UserObjectPermission = apps.get_model("guardian", "UserObjectPermission")
    organization_content_type = ContentType.objects.get(model="organization")

    for rule in rules_objects:
        user = rule.user
        permissions = permissions_objects_matrix.get(rule.role.title)
        with transaction.atomic():
            for perm in permissions:
                try:
                    UserObjectPermission.objects.get_or_create(
                        content_type=organization_content_type,
                        object_pk=rule.object_id,
                        user=user,
                        permission=perm,
                    )
                except IntegrityError:
                    # means that duplicate key and value and the permission already assigned
                    pass


def store_organization_settings(organization):
    organization.settings = {
        "name": organization.name,
        "full_logo": organization.full_logo.url.url
        if organization.full_logo is not None
        else None,
        "abstract_logo": None,
        **organization.meta,
    }
    organization.save()


def create_organization_acl_rules(apps, organization):
    ContentType = apps.get_model("contenttypes", "ContentType")
    Rule = apps.get_model("acl", "Rule")
    Group = apps.get_model("auth", "Group")
    organization_content_type = ContentType.objects.get(model="organization")

    # 1. get organizations group users
    for grp in Group.objects.filter(
        name__startswith=f"{organization.pk}#Organization "
    ):
        role_title = grp.name.split("#")[-1]
        role_object = roles_objects[role_title]
        for user in grp.user_set.all():
            rule, _ = Rule.objects.get_or_create(
                content_type=organization_content_type,
                object_id=organization.pk,
                role=role_object,
                user=user,
            )
            rules_objects.append(rule)


def delete_organization_groups(apps, organization):
    """delete organizations groups, This will CASCADE delete the object permissions"""
    Group = apps.get_model("auth", "Group")
    Group.objects.filter(name__startswith=f"{organization.pk}#Organization ").delete()


def migrate_organizations_rules(apps):
    """Restore and migrate the old roles and rules to gabbro.acl tables"""
    Organization = apps.get_model("organizations", "Organization")

    for org in Organization.objects.all():
        # Store organizations settings
        store_organization_settings(org)
        # create gabbro acl rules for users and groups
        create_organization_acl_rules(apps, org)
        # delete organizations groups
        delete_organization_groups(apps, org)


def create_gabbro_acl_rules(apps, schema_editor):
    # create organizations' roles
    create_organization_roles(apps)
    # migrate organizations' rules to use gabbro.acl.Rule
    migrate_organizations_rules(apps)
    # manually assign permissions, because django does not fire signals events
    assign_rules_permissions(apps)


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0008_auto_20220306_1507"),
    ]

    add_fields_operations = [
        migrations.AddField(
            model_name="organization",
            name="created",
            field=django_extensions.db.fields.CreationDateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="created",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="organization",
            name="external_id",
            field=models.PositiveIntegerField(
                null=True, unique=True, verbose_name="Accounts' Organization Id"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="modified",
            field=django_extensions.db.fields.ModificationDateTimeField(
                auto_now=True, verbose_name="modified"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="settings",
            field=models.JSONField(
                blank=True,
                default=gabbro.organizations.models.get_default_settings,
                verbose_name="Organization Settings",
            ),
        ),
    ]
    remove_fields_operations = [
        migrations.RemoveField(
            model_name="organization",
            name="created_at",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="meta",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="name",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="updated_at",
        ),
    ]
    operations = [
        *add_fields_operations,
        migrations.RunPython(create_gabbro_acl_rules, migrations.RunPython.noop),
        *remove_fields_operations,
    ]
