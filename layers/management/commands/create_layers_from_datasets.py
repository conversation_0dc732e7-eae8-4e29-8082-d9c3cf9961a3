import logging
import threading
import time
from uuid import uuid4

from django.core.files.storage import default_storage
from django.core.management import BaseCommand, CommandError

from common.utils import calculate_boundaries_postgis
from layers.management.commands.load_tataba_layers_from_geopackage import (
    geopackage_to_geojson_generator,
    create_records_from_features,
)
from layers.models import Layer, SLD
from workspaces.models import Workspace, Dataset
from workspaces.strategies import CreateUploadFileRequestStrategy

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("--workspace_id", type=str, required=True)
        parser.add_argument(
            "--datasets_paths",
            type=lambda s: [item.strip() for item in s.split("-")],
            required=True,
        )

    def handle(self, *args, **options):
        start = time.time()
        workspace_id = options["workspace_id"]
        datasets_paths = options["datasets_paths"]
        logger.debug(f"Loading layers from datasets: {datasets_paths}")
        workspace = Workspace.objects.filter(id=workspace_id).first()
        if not workspace:
            raise CommandError(f"The workspace_id must be a valid id: {workspace_id}")
        validate_paths(datasets_paths)
        layers_ids = loading_layers(datasets_paths, workspace)
        fill_datasets_and_layers_with_geocore_data(
            workspace=workspace, layers_ids=layers_ids
        )
        end = time.time()
        self.stdout.write(f"All Layers Loaded in {end-start} seconds")


def validate_paths(datasets_paths: list[str]):
    for dataset_path in datasets_paths:
        if not default_storage.exists(dataset_path):
            raise CommandError(f"this dataset_path not Found {dataset_path}")


def loading_layers(datasets_paths: list[str], workspace: Workspace):
    created_layers_ids = []
    for dataset_path in datasets_paths:
        dataset_file = default_storage.open(dataset_path)
        dataset, _ = Dataset.objects.get_or_create(file=f"{dataset_file}")
        logger.debug(f"Dataset created {dataset.id}")
        layer, _ = Layer.objects.get_or_create(
            workspace=workspace,
            dataset=dataset,
            defaults={"key": f"{workspace.pk} - {str(uuid4()).split('-')[0]}"},
        )
        sld = SLD.objects.create(title=layer.key)
        layer.slds.add(sld)
        logger.debug(f"Layer created {layer.id}")
        created_layers_ids.append(layer.id)
        features_list = geopackage_to_geojson_generator(file_object=dataset_file)
        create_records_from_features(features_list=features_list, layer=layer)
        calculate_layer_boundaries(layer=layer)
        if record := layer.records.filter(map_data__isnull=False).first():
            layer.data["summary_fields"] = list(record.map_data.keys())[:3]
            layer.data["columns"] = list(record.map_data.keys())
            layer.save()
        logger.debug(f"Finished of loading the file: {dataset_path}")
        logger.debug("=" * 70)
    logger.debug(f"Created layers ids: {created_layers_ids}")
    return created_layers_ids


def fill_datasets_and_layers_with_geocore_data(
    workspace: Workspace, layers_ids: list[int]
):
    logger.debug("Filling datasets with geocore data")
    logger.debug("Starting ......")
    logger.debug(f"Updating Workspace: {workspace.id}")
    layers = workspace.layers.filter(
        id__in=layers_ids, eda_reports__isnull=True
    ).select_related("dataset")
    logger.debug(f"Updating {len(layers)} layers without EDA report")
    for layer in layers:
        strategy = CreateUploadFileRequestStrategy()
        thread = threading.Thread(
            target=strategy.process_and_store_dataset_metadata,
            args=(layer.dataset, layer.title),
        )
        thread.start()
        thread.join()
        layer.refresh_from_db()
        if eda_reports := layer.dataset.meta_data["eda_reports"]:
            layer.dataset.create_eda_reports(eda_reports)
    logger.debug("Datasets filled with geocore data")
    logger.debug("Exiting ....")


def calculate_layer_boundaries(layer: Layer):
    layer.boundaries = calculate_boundaries_postgis(
        records=layer.records.only("geometry")
    )
    layer.save(update_fields=["boundaries"])
    logger.debug(f"Layer boundaries calculated: {layer.id}")
