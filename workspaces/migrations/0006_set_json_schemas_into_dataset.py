# Generated by Django 3.2.25 on 2024-12-24 12:06
import logging

from django.db import migrations

logger = logging.getLogger("workspaces")


def set_json_schemas_into_dataset(apps, schema_editor):
    Dataset = apps.get_model("workspaces", "Dataset")
    datasets = Dataset.objects.filter(
        meta_data__json_schema__isnull=True
    ).prefetch_related("layers")
    logger.debug(f"[set_json_schemas_into_dataset] datasets: {len(datasets)}")
    for dataset in datasets:
        layer = dataset.layers.filter(
            location_field_mapping__isnull=False,
            json_schema__isnull=False,
            data__summary_fields__isnull=False,
        ).last()
        if not layer:
            continue
        dataset.title = layer.title
        dataset.meta_data = {
            **dataset.meta_data,
            **{
                "json_schema": layer.json_schema,
                "web_ui_json_schema": layer.web_ui_json_schema,
                "summary_fields": layer.data.get("summary_fields"),
                "location_field_mapping": layer.location_field_mapping,
                "color": layer.color,
            },
        }
    Dataset.objects.bulk_update(datasets, ["meta_data", "title"])
    logger.debug("json_schemas set into datasets")


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0005_dataset_name"),
        ("layers", "0012_alter_layer_dataset"),
    ]

    operations = [
        migrations.RunPython(set_json_schemas_into_dataset, migrations.RunPython.noop)
    ]
