import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import BaseTestMixin
from workspaces.models import (
    WorkspaceRequest,
    WorkspaceRequestChoices,
    RequestTypeChoices,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestCreateWorkspaceRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset_file = os.path.join(settings.MEDIA_ROOT, "sample_geojson.geojson")
        self.mutation = """
        mutation MyMutation($title: String!, $description: String, $color: String!, $datasetFile: String!, $orgId: Int!, $readOnly: <PERSON><PERSON><PERSON>) {
          createDataset(
            dataInput: {title: $title, description: $description, color: $color, datasetFile: $datasetFile, orgId: $orgId, readOnly: $readOnly}
          ) {
            datasetRequest {
              id
              status
              requestType
              layerData
              createdBy {
                id
                email
              }
             
            }
          }
        }
        """
        self.variables = {
            "title": "Test Dataset",
            "description": "Test Description",
            "color": "#e3cec7",
            "datasetFile": self.dataset_file,
            "orgId": self.organization.id,
            "readOnly": False,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot create a workspace request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_file_extension(self):
        """Test that invalid file extensions are rejected."""
        # Set an invalid file extension
        self.variables["datasetFile"] = "invalid_file.txt"

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query fails with a file extension error
        self.assertDictEqual(
            {
                "http": {
                    "reason": {
                        "datasetFile": "Dataset file extension must be on of csv, gpkg, geojson, kml, shp"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
            response["errors"][0]["extensions"],
        )

    def test_missing_required_fields(self):
        """Test that required fields are validated."""
        # Remove required fields
        variables_without_title = self.variables.copy()
        del variables_without_title["title"]

        response = self.client.execute(
            self.mutation, variables=variables_without_title, context=self.auth_request
        )
        # Check if the query fails with a required field error
        self.assertIn("errors", response)
        self.assertIn("title", response["errors"][0]["message"])

    def test_successful_creation(self):
        """Test successful creation of a workspace request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["createDataset"]["datasetRequest"]
        self.assertEqual(
            data["status"], WorkspaceRequestChoices.IN_PROGRESS.value.upper()
        )
        self.assertEqual(
            data["requestType"], RequestTypeChoices.UPLOAD_FILE.value.upper()
        )
        self.assertEqual(data["createdBy"]["id"], self.user.id)

        # Verify the layer data
        layer_data = data["layerData"]
        self.assertEqual(layer_data["title"], self.variables["title"])
        self.assertEqual(layer_data["description"], self.variables["description"])
        self.assertEqual(layer_data["color"], self.variables["color"])

        # Verify the database record
        workspace_request = WorkspaceRequest.objects.get(id=data["id"])
        self.assertEqual(workspace_request.status, WorkspaceRequestChoices.IN_PROGRESS)
        self.assertEqual(workspace_request.request_type, RequestTypeChoices.UPLOAD_FILE)
        self.assertEqual(workspace_request.created_by, self.user)
        self.assertEqual(workspace_request.organization, self.organization)
