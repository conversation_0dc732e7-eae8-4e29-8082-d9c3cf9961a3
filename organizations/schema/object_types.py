import graphene
from graphene_django import DjangoObjectType
from graphene_gis.scalars import JSONScalar

from common.utils.models import get_owner_user
from organizations.models import Organization
from users.models import ActiveStatusChoices


class OrganizationType(DjangoObjectType):
    settings = graphene.Field(JSONScalar)
    users_count = graphene.Int()

    class Meta:
        model = Organization
        fields = ["id", "external_id", "settings", "created", "modified"]

    def resolve_users_count(self: Organization, info):
        owner_user = get_owner_user(self)
        users = self.acl_individuals.exclude(
            active_status=ActiveStatusChoices.DELETED.value
        )
        if owner_user:
            users = users.exclude(id=owner_user.id)
        return users.count()


class OrganizationListType(graphene.ObjectType):
    data = graphene.List(OrganizationType)
    count = graphene.Int()
