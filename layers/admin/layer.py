from django.contrib import admin

from layers.models import Layer, SLD


@admin.register(Layer)
class LayerAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "key",
        "title",
        "status",
        "workspace",
        "dataset",
        "read_only",
        "created",
    )
    list_display_links = ("id", "key")
    search_fields = ("id", "key", "title", "description")
    list_filter = ("status", "created")

    def get_readonly_fields(self, request, obj=None):
        fields = super().get_readonly_fields(request, obj)
        if obj:
            return fields + ("key",)
        return fields

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(SLD)
class SLDAdmin(admin.ModelAdmin):
    list_display = ("id", "title", "sld_type", "created")
    list_display_links = ("id", "title")
    search_fields = ("id", "title")
    list_filter = ("sld_type", "created")
