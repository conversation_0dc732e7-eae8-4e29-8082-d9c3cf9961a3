import os
from datetime import timedelta

from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceFactory,
    BaseTestMixin,
    LayerFactory,
    UserFactory,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestUpdateWorkSpace(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace for testing
        self.workspace = WorkspaceFactory(
            owner=self.user,
            organization=self.organization,
            name="Test Workspace",
            description="Test Description",
        )

        # Create some layers for the workspace
        self.layer1 = LayerFactory(workspace=self.workspace)
        self.layer2 = LayerFactory(workspace=self.workspace)
        self.layer3 = LayerFactory(workspace=self.workspace)

        self.mutation = """
        mutation MyMutation($workspaceId: Int!, $orgId: Int!, $name: String, $description: String, $thumbnail: String, $layersSortedIds: [Int], $updateVisitedDate: Boolean) {
          updateWorkspace(
            dataInput: {workspaceId: $workspaceId, orgId: $orgId, name: $name, description: $description, thumbnail: $thumbnail, layersSortedIds: $layersSortedIds, updateVisitedDate: $updateVisitedDate}
          ) {
            workspace {
              id
              name
              description
              thumbnail
              lastVisited
            }
          }
        }
        """
        self.variables = {
            "workspaceId": self.workspace.id,
            "orgId": self.organization.id,
            "name": "Updated Workspace Name",
            "description": "Updated Description",
            "thumbnail": "https://example.com/new_thumbnail.jpg",
            "layersSortedIds": [self.layer3.id, self.layer1.id, self.layer2.id],
            "updateVisitedDate": True,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot update a workspace."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_workspace_id(self):
        """Test that an invalid workspace ID is handled properly."""
        # Use a non-existent workspace ID
        invalid_variables = self.variables.copy()
        invalid_variables["workspaceId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a workspace error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceId": "Workspace with 99999 not found"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_invalid_layers_sorted_ids(self):
        """Test that invalid layers_sorted_ids are handled properly."""
        # Use a non-existent layer ID
        invalid_variables = self.variables.copy()
        invalid_variables["layersSortedIds"] = [self.layer1.id, 99999, self.layer3.id]

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a layers_sorted_ids error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "layersSortedIds": "Layers sorted ids must be a subset of workspace layers"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_workspace_regular_user(self):
        """Test that a regular user cannot update another user's workspace."""
        # Create a workspace owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace = WorkspaceFactory(
            owner=other_user,
            organization=self.organization,
            name="Other User Workspace",
            description="Other User Description",
        )

        # Try to update the other user's workspace
        other_variables = self.variables.copy()
        other_variables["workspaceId"] = other_workspace.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # The mutation should execute but the user should not have permission
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "layersSortedIds": "Layers sorted ids must be a subset of workspace layers"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_workspace_superuser(self):
        """Test that a superuser can update another user's workspace."""
        # Create a workspace owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace = WorkspaceFactory(
            owner=other_user,
            organization=self.organization,
            name="Other User Workspace",
            description="Other User Description",
        )

        # Try to update the other user's workspace as a superuser
        other_variables = {
            "workspaceId": other_workspace.id,
            "orgId": self.organization.id,
            "name": "Updated Other Workspace Name",
            "description": "Updated Other Description",
            "thumbnail": "https://example.com/other_thumbnail.jpg",
            "updateVisitedDate": True,
        }

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)

        # Verify the workspace was updated
        data = response["data"]["updateWorkspace"]["workspace"]
        self.assertEqual(data["name"], other_variables["name"])
        self.assertEqual(data["description"], other_variables["description"])
        self.assertEqual(data["thumbnail"], other_variables["thumbnail"])

        # Verify the database record
        other_workspace.refresh_from_db()
        self.assertEqual(other_workspace.name, other_variables["name"])
        self.assertEqual(other_workspace.description, other_variables["description"])
        self.assertEqual(other_workspace.thumbnail, other_variables["thumbnail"])

    def test_update_name_description_thumbnail(self):
        """Test successful update of workspace name, description, and thumbnail."""
        update_variables = {
            "workspaceId": self.workspace.id,
            "orgId": self.organization.id,
            "name": "New Name",
            "description": "New Description",
            "thumbnail": "https://example.com/new_thumbnail.jpg",
        }

        response = self.client.execute(
            self.mutation, variables=update_variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["updateWorkspace"]["workspace"]
        self.assertEqual(data["name"], update_variables["name"])
        self.assertEqual(data["description"], update_variables["description"])
        self.assertEqual(data["thumbnail"], update_variables["thumbnail"])

        # Verify the database record
        self.workspace.refresh_from_db()
        self.assertEqual(self.workspace.name, update_variables["name"])
        self.assertEqual(self.workspace.description, update_variables["description"])
        self.assertEqual(self.workspace.thumbnail, update_variables["thumbnail"])

    def test_update_layers_sorted_ids(self):
        """Test successful update of layers_sorted_ids."""
        update_variables = {
            "workspaceId": self.workspace.id,
            "orgId": self.organization.id,
            "name": self.workspace.name,
            "description": self.workspace.description,
            "layersSortedIds": [self.layer2.id, self.layer3.id, self.layer1.id],
        }

        response = self.client.execute(
            self.mutation, variables=update_variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the database record
        self.workspace.refresh_from_db()
        # Note: We can't directly check the layers_sorted_ids in the response
        # because it's not returned in the GraphQL response.
        # We would need to check the database or add it to the response.

    def test_update_visited_date(self):
        """Test successful update of last_visited date."""
        # Record the current last_visited date
        old_last_visited = self.workspace.last_visited

        # Wait a moment to ensure the timestamp changes
        update_variables = {
            "workspaceId": self.workspace.id,
            "orgId": self.organization.id,
            "name": self.workspace.name,
            "description": self.workspace.description,
            "updateVisitedDate": True,
        }

        response = self.client.execute(
            self.mutation, variables=update_variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the last_visited date was updated
        self.workspace.refresh_from_db()
        self.assertNotEqual(self.workspace.last_visited, old_last_visited)

        # The last_visited date should be recent
        self.assertLess(
            timezone.now() - self.workspace.last_visited, timedelta(seconds=10)
        )

    def test_successful_update_all_fields(self):
        """Test successful update of all workspace fields."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["updateWorkspace"]["workspace"]
        self.assertEqual(data["name"], self.variables["name"])
        self.assertEqual(data["description"], self.variables["description"])
        self.assertEqual(data["thumbnail"], self.variables["thumbnail"])

        # Verify the database record
        self.workspace.refresh_from_db()
        self.assertEqual(self.workspace.name, self.variables["name"])
        self.assertEqual(self.workspace.description, self.variables["description"])
        self.assertEqual(self.workspace.thumbnail, self.variables["thumbnail"])

        # The last_visited date should be recent
        self.assertLess(
            timezone.now() - self.workspace.last_visited, timedelta(seconds=10)
        )
