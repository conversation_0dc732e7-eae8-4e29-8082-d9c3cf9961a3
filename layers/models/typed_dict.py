from typing import TypedDict


class LayerSLD(TypedDict):
    color: str
    opacity: float
    stroke_color: str
    stroke_width: int
    stroke_opacity: float


class HeatMap(TypedDict):
    weight_field: str
    color_range: list[str]
    opacity: float
    radius: int
    density_range: list[float]


def get_default_sld_data():
    return LayerSLD(
        color="#B22222",
        opacity=0.5,
        stroke_color="#B22222",
        stroke_width=1,
        stroke_opacity=0.2,
    )
