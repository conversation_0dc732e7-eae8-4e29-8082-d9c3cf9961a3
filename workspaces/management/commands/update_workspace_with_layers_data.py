import logging

from django.core.management.base import BaseCommand

from workspaces.models import Workspace

logger = logging.getLogger("workspaces")


class Command(BaseCommand):
    help = "Updates the layers_count and records_count for workspaces and updates layers_data"

    def handle(self, *args, **kwargs):
        logger.debug("Updating workspaces with layers_count and records_count...")

        # Fetch all workspaces
        workspaces = Workspace.objects.only("id", "layers_data")

        for workspace in workspaces:
            layers_count = workspace.layers.count()

            # Assuming the layers have a related field `records` that can be counted
            records_count = sum(
                layer.records.count() for layer in workspace.layers.all()
            )

            # Update layers_data with the calculated values
            workspace.layers_data = {
                "layers_count": layers_count,
                "records_count": records_count,
            }
            logger.debug(
                f"Updated workspace {workspace.id} -> layers: {layers_count}, records: {records_count}"
            )
        Workspace.objects.bulk_update(workspaces, ["layers_data", "modified"])
        logger.debug(f"Completed updates for {workspaces.count()} workspaces.")
