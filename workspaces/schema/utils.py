from django.conf import settings

from common.handlers.dataset_files import DatasetFilesLoader
from common.utils import (
    parse_all_properties,
    get_json_schema_errors,
    get_predicted_jsonschema,
)
from workspaces.enums import LongLatEnum
from workspaces.models import WorkspaceRequest, Dataset


def get_predicted_jsonschema_from_dataset(dataset_request: WorkspaceRequest):
    # Get geometry columns
    location_field_mapping = dataset_request.layer_data["location_field_mapping"]
    if location_field_mapping["lat_lon_column_num"] == LongLatEnum.column.value:
        geometry_columns = location_field_mapping["lang_lat_column"]
    else:
        geometry_columns = [
            location_field_mapping["longitude_column"],
            location_field_mapping["latitude_column"],
        ]

    # Loading data from dataset file
    loader = DatasetFilesLoader(
        file=dataset_request.dataset.file, excluded_columns=geometry_columns
    )
    df = loader.load_data(rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER)
    data = [parse_all_properties(record) for record in df.to_dict(orient="records")]

    # Get the predicted schema from dataset data
    json_schema = get_predicted_jsonschema(data=data, with_enums=len(data) > 20)
    if get_json_schema_errors(json_schema):
        json_schema = dict()

    return data, json_schema


def get_first_column_from_layer(dataset: Dataset):
    columns = dataset.meta_data.get("columns")
    return [columns[0]] if columns else []


def mapping_sample_data_with_json_schem_title(sample_data, json_schema):
    properties = json_schema.get("properties", dict())
    for data in sample_data:
        column = data.get("column")
        data["title"] = properties.get(column, dict()).get("title")
    return sample_data


def mapping_columns_with_json_schema_title(columns: list[str], json_schema: dict):
    properties = json_schema.get("properties", dict())
    return [(column, properties.get(column, dict()).get("title")) for column in columns]
