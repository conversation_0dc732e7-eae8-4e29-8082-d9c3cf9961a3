.PHONY: db-connect
db-connect:
	PGPASSWORD=passw0rd pgcli -h 127.0.0.1 -p 5432 -U postgres geocore_backend

.PHONY: clean
clean:
	find . -name "*.pyc" -type f -delete
	find . -name "*.py,cover" -type f -delete

.PHONY: test-coverage
test-coverage:
	coverage erase && coverage run manage.py test --parallel=40; coverage combine && coverage report

.PHONY: docker-build-push
docker-build-push:
	docker build -t "gcr.io/geocore-cloud/backend:$(tag)" .
	docker push gcr.io/geocore-cloud/backend:$(tag)

.PHONY: docker-build-push-gis-middleware
docker-build-push-gis-middleware:
	docker build -t "gcr.io/geocore-cloud/gis-middleware:$(tag)" . -f nginx/Dockerfile
	docker push gcr.io/geocore-cloud/gis-middleware:$(tag)
