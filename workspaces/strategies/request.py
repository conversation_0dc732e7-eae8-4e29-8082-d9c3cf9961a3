import json
import logging
from threading import Thread
from typing import List, Optional

from django.conf import settings
from django.contrib.gis.geos import Point
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest
from gabbro.layers_engine.enums import LayerStatus
from shapely.geometry.base import BaseGeometry

from common.handlers.dataset_files import DatasetFilesLoader
from common.interfaces import Strategy
from common.utils import (
    geometry_collection_from_string,
)
from layers.mixins import LayerMixin
from layers.scripts import PublishLayerToGeoserver
from organizations.models import Organization
from users.models import User
from workspaces.enums import LongLatEnum
from workspaces.mixins import (
    WorkspaceMixin,
    DatasetMixin,
    loading_meta_data_from_dataset,
    generate_eda_report_from_dataset,
)
from workspaces.models import Dataset, WorkspaceRequest, RequestTypeChoices
from workspaces.serializers import (
    LocationFieldMappingSerializer,
    WorkspaceRequestSerializer,
)

logger = logging.getLogger("workspaces")
MEDIA_URL = getattr(settings, "MEDIA_URL")


class CreateUploadFileRequestStrategy(Strategy, DatasetMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ) -> WorkspaceRequest:
        dataset_file = data_input["dataset_file"]
        workspace = context.get("workspace")
        dataset = self.create_dataset(
            title=data_input["title"], file=dataset_file, **{"workspace": workspace}
        )
        self.debug("handle", f"dataset created: {dataset}")
        dataset_request = self.validate_and_create_dataset_layer_data(
            user, dataset, data_input
        )
        self.debug("handle", f"dataset_request created: {dataset_request}")
        dataset.set_metadata(
            {
                "eda_reports": {
                    "status": LayerStatus.REPORT_GENERATE_PENDING.value,
                    "errors": [],
                }
            }
        )
        loading_meta_data_from_dataset(dataset=dataset)
        background_thread = Thread(
            target=generate_eda_report_from_dataset,
            args=(dataset.id, dataset_request.layer_data["title"]),
        )
        background_thread.start()
        return dataset_request

    @staticmethod
    def validate_and_create_dataset_layer_data(
        user: User, dataset: Dataset, data_input: dict
    ) -> WorkspaceRequest:
        serializer = WorkspaceRequestSerializer(
            data={
                "created_by": user.id,
                "layer_data": data_input,
                "organization": data_input["org_id"],
                "request_type": RequestTypeChoices.UPLOAD_FILE.value,
            },
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        serializer.validated_data["dataset"] = dataset
        return serializer.save()


class CreateLocationFieldMappingStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        dataset_request: WorkspaceRequest = context["dataset_request"]
        validated_data = self.validate_location_field_mapping_data(data_input)
        self.inject_location_field_mapping_into_layer_data(
            dataset_request, validated_data
        )
        self.debug(
            "handle",
            f"location_field_mapping saved in dataset_request: {dataset_request.layer_data['location_field_mapping']}",
        )
        geo_columns = self.get_columns(validated_data)
        sample_data = self.get_sample_data_from_dataset_request(
            dataset_request, geo_columns
        )
        if not sample_data:
            sample_data = self.get_sample_data_from_dataset_file(
                dataset_request, geo_columns
            )

        self.validate_sample_data(sample_data, geo_columns)
        return dataset_request

    @staticmethod
    def get_sample_data_from_dataset_request(
        dataset_request: WorkspaceRequest, geo_columns: List[str]
    ) -> Optional[List[dict]]:
        sample_data = dataset_request.dataset.meta_data.get("sample_data")
        if not sample_data:
            return None

        exported_sample_data = [
            next(
                (item for item in sample_data if item["column"] == geo_column),
                None,
            )
            for geo_column in geo_columns
        ]

        if not all(exported_sample_data):
            raise BadRequest(
                reason={
                    "error": _("columns %(columns)s not included in the dataset")
                    % {"columns": geo_columns}
                }
            )
        return exported_sample_data

    @staticmethod
    def get_sample_data_from_dataset_file(
        dataset_request: WorkspaceRequest, geo_columns: List[str] = None
    ) -> Optional[List[dict]]:
        try:
            loader = DatasetFilesLoader(
                file=dataset_request.dataset.file, columns=geo_columns
            )
            sample_data = loader.get_sample_data(rows_number=1)
        except Exception:
            raise BadRequest(
                reason={
                    "error": _("columns %(columns)s not included in the dataset")
                    % {"columns": geo_columns}
                }
            )
        return sample_data

    @staticmethod
    def validate_sample_data(sample_data, columns):
        try:
            geometry = sample_data[0]["data"][0]
            if len(columns) == 1:
                if isinstance(geometry, BaseGeometry):
                    assert geometry.is_valid
                    return
                else:
                    geometry = geometry_collection_from_string(
                        json.dumps(sample_data[0]["data"][0])
                    )
            else:
                geometry = Point(
                    float(sample_data[1]["data"][0]), float(sample_data[0]["data"][0])
                )
            assert geometry.valid
        except Exception:
            raise BadRequest(
                reason={
                    "error": _("Invalid geometry columns %(columns)s")
                    % {"columns": columns}
                }
            )

    @staticmethod
    def get_columns(data) -> List[str]:
        if data["lat_lon_column_num"] == LongLatEnum.column.value:
            return [data["lang_lat_column"]]
        return [data["latitude_column"], data["longitude_column"]]

    @staticmethod
    def validate_location_field_mapping_data(data_input):
        serializer = LocationFieldMappingSerializer(data=data_input)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return serializer.validated_data

    @staticmethod
    def inject_location_field_mapping_into_layer_data(
        dataset_request: WorkspaceRequest, location_field_mapping
    ):
        dataset_request.layer_data["location_field_mapping"] = location_field_mapping
        dataset_request.save(update_fields=["layer_data", "modified"])
        dataset_request.dataset.set_metadata(
            {"location_field_mapping": location_field_mapping}
        )


class UpdateJSONSchemasStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        dataset_request: WorkspaceRequest = context["dataset_request"]
        dataset_request.set_layer_data(
            layer_data={
                "json_schema": data_input["json_schema"],
                "web_ui_json_schema": data_input.get("web_ui_json_schema"),
            }
        )
        dataset_request.dataset.set_metadata(
            {
                "json_schema": data_input["json_schema"],
                "web_ui_json_schema": data_input.get("web_ui_json_schema"),
            }
        )
        self.debug("handle", f"json_schema saved in dataset_request: {dataset_request}")
        return dataset_request


class CreateDesignLayerRequestStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        serializer = WorkspaceRequestSerializer(
            data={
                "created_by": user.id,
                "layer_data": data_input,
                "organization": data_input["org_id"],
                "request_type": RequestTypeChoices.DESIGN_LAYER.value,
            },
        )
        serializer.is_valid(raise_exception=False)
        return serializer.save()


class UpdateDesignLayerRequestStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        workspace_request: WorkspaceRequest = context["workspace_request"]
        serializer = WorkspaceRequestSerializer(
            instance=workspace_request,
            partial=True,
            data={"layer_data": data_input},
        )
        serializer.is_valid(raise_exception=False)
        return serializer.save()


class DesignLayerJsonSchemaStrategy(Strategy, WorkspaceMixin, LayerMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        workspace_request: WorkspaceRequest = context["workspace_request"]
        columns = list(data_input["json_schema"]["properties"].keys())
        workspace_request.set_layer_data(
            layer_data={"json_schema": data_input["json_schema"]}
        )
        self.debug(
            "handle", f"json_schema saved in workspace_request: {workspace_request}"
        )
        workspace = context.get("workspace")
        if not workspace:
            workspace = self.create_workspace(
                workspace_request=workspace_request,
                organization=workspace_request.organization,
            )
        layer, sld = self.create_layer(
            workspace_request=workspace_request,
            workspace=workspace,
            summary_fields=data_input["summary_fields"],
            columns=columns,
        )
        workspace.increase_layers_count()
        workspace_request.finish(layer=layer)

        publisher = PublishLayerToGeoserver(sld=sld, layer=layer)
        publisher.publish()
        return workspace
