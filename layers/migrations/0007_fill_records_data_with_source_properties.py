# Generated by Django 3.2.25 on 2024-11-26 11:54

from django.db import migrations

from common.utils import slice_list_to_chunks


def fill_records_data_with_source_properties(apps, schema_editor):
    Record = apps.get_model("layers", "Record")
    records = list(Record.objects.only("id", "data", "source_properties"))
    for sliced_records in slice_list_to_chunks(records):
        for record in sliced_records:
            record.data = record.source_properties
        Record.objects.bulk_update(objs=sliced_records, fields=["data"])


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0006_fill_layers_data_with_summary_fields"),
    ]

    operations = [
        migrations.RunPython(
            fill_records_data_with_source_properties, migrations.RunPython.noop
        )
    ]
