from abc import ABC, abstractmethod

from organizations.models import Organization
from users.models import User


class InputValidation(ABC):
    @abstractmethod
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        """Method that all order validators must implement"""
        raise NotImplementedError()


class QueryValidation(ABC):
    @abstractmethod
    def validate_and_get_data(self, slug):
        """Method that all order validators must implement"""
        raise NotImplementedError()
