import logging

from django.core.management import BaseCommand

from common.utils import get_predicted_jsonschema
from layers.models import Layer

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "--layer_ids",
            type=lambda s: [int(item.strip()) for item in s.split(",")],
            required=False,
            help="Import Specific Layers",
        )

    def handle(self, *args, **options):
        logger.debug("Starting ......")
        layer_ids = options.get("layer_ids")
        if layer_ids:
            layers = Layer.objects.filter(id__in=layer_ids).prefetch_related("records")
        else:
            layers = Layer.objects.prefetch_related("records")

        for layer in layers:
            records = layer.records.values_list("data", flat=True)
            json_schema = get_predicted_jsonschema(records, len(records) > 20)
            logger.debug(
                f"Layer pk: {layer.pk}, records: {len(records)}, json_schema: {json_schema}"
            )
            if json_schema:
                layer.json_schema = json_schema
        Layer.objects.bulk_update(layers, ["json_schema"])
        logger.debug(self.style.NOTICE("Exiting ......"))
