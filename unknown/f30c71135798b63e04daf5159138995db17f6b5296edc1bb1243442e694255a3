import logging
from threading import Thread

from django.db import transaction
from django.utils import timezone
from gabbro.graphene import BadRequest

from chat_ai.models import Conversation
from common.interfaces import Strategy
from layers.mixins import LayerMixin
from layers.models import Layer
from layers.scripts import PublishLayerToGeoserver
from organizations.models import Organization
from users.models import User
from workspaces.mixins import WorkspaceMixin
from workspaces.models import Workspace, WorkspaceRequest
from workspaces.schema import get_predicted_jsonschema_from_dataset
from workspaces.schema.utils import (
    get_first_column_from_layer,
    mapping_sample_data_with_json_schem_title,
)
from workspaces.serializers import WorkspaceSerializer

logger = logging.getLogger("workspaces")


class CreateWorkSpaceStrategy(Strategy, LayerMixin, WorkspaceMixin):
    def __init__(self):
        super().__init__()
        self._logger = logger

    @transaction.atomic
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        dataset_request: WorkspaceRequest = context["dataset_request"]
        dataset = dataset_request.dataset
        workspace = dataset.workspace
        summary_fields = data_input.get(
            "map_data_columns"
        ) or get_first_column_from_layer(dataset)
        if not workspace:
            workspace = self.create_workspace(
                workspace_request=dataset_request,
                organization=dataset_request.organization,
            )
            dataset.set_workspace(workspace)

        # Generate the JSON Schema if not exist
        json_schema = dataset_request.layer_data.get("json_schema")
        if not json_schema:
            _, json_schema = get_predicted_jsonschema_from_dataset(dataset_request)
            dataset_request.set_layer_data(dict(json_schema=json_schema))

        # Update the Sample Data & Summary Fields
        sample_data = mapping_sample_data_with_json_schem_title(
            dataset.meta_data.get("sample_data"), json_schema
        )
        dataset.set_metadata(
            dict(summary_fields=summary_fields, sample_data=sample_data)
        )

        # Create the Layer
        layer, sld = self.create_layer(
            workspace_request=dataset_request,
            workspace=workspace,
            summary_fields=summary_fields,
        )
        workspace.increase_layers_count()
        dataset_request.finish(layer=layer)

        publisher = PublishLayerToGeoserver(sld=sld, layer=layer)
        publisher.publish()
        # Background JOB
        # TODO: Use Celery instead of threading
        Thread(
            target=self.process_layer_records, args=(layer, workspace, summary_fields)
        ).start()
        return workspace

    def process_layer_records(
        self, layer: Layer, workspace: Workspace, summary_fields: list[str]
    ):
        geometry_columns = self.get_geometry_columns_from_layer(layer=layer)
        try:
            self.create_geometry_records_from_dataset_file(
                layer=layer,
                geometry_columns=geometry_columns,
                summary_fields=summary_fields,
            )
        except Exception as e:
            logger.error(e)
        self.update_layer_boundaries(layer=layer)
        workspace.increase_records_count(layer.records.count())


class UpdateWorkSpaceStrategy(Strategy):
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        workspace: Workspace = context["workspace"]
        if data_input.get("update_visited_date"):
            data_input["last_visited"] = timezone.now()
        serializer = WorkspaceSerializer(
            instance=workspace, data=data_input, partial=True
        )
        serializer.is_valid()
        return serializer.save()


class DeleteWorkspaceStrategy(Strategy):
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        workspace: Workspace = context["workspace"]
        layers = workspace.layers.only("id")
        layers_ids = layers.values_list("id", flat=True)
        Conversation.objects.filter(layers__id__in=layers_ids).delete()
        layers.delete()
        workspace.delete()


class CreateEmptyWorkspaceStrategy(Strategy):
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        data = {"organization": organization.id, "owner": user.id, **data_input}
        serializer = WorkspaceSerializer(data=data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return serializer.save()
