# Generated by Django 3.1.3 on 2021-05-18 05:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200, verbose_name="name")),
            ],
            options={
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
                "permissions": (
                    ("view_user", "Can view user"),
                    ("add_user", "Can add user"),
                    ("change_user", "Can change user"),
                    ("delete_user", "Can delete user"),
                    ("view_layer", "Can view layer"),
                    ("add_layer", "Can add layer"),
                    ("delete_layer", "Can delete layer"),
                    ("edit_layer", "Can edit layer"),
                    ("view_record", "Can view record"),
                    ("add_record", "Can add record"),
                    ("delete_record", "Can delete record"),
                    ("edit_record", "Can edit record"),
                ),
            },
        ),
    ]
