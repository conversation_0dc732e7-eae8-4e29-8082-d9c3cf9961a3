import os

from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import BaseTestMixin, UserFactory, WorkspaceRequestFactory
from workspaces.models import (
    WorkspaceRequest,
    WorkspaceRequestChoices,
    RequestTypeChoices,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestUpdateDesignLayerRequest(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace request to update in the tests
        self.workspace_request = WorkspaceRequestFactory(
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Original Design Layer",
                "description": "Original Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        self.mutation = """
        mutation MyMutation($workspaceRequestId: Int!, $title: String!, $description: String, $color: String!, $orgId: Int!, $readOnly: Boolean) {
          updateDesignLayerRequest(
            dataInput: {workspaceRequestId: $workspaceRequestId, title: $title, description: $description, color: $color, orgId: $orgId, readOnly: $readOnly}
          ) {
            workspaceRequest {
              id
              status
              requestType
              layerData
              createdBy {
                id
                email
              }
            }
          }
        }
        """
        self.variables = {
            "workspaceRequestId": self.workspace_request.id,
            "title": "Updated Design Layer",
            "description": "Updated Description",
            "color": "#e3cec7",
            "orgId": self.organization.id,
            "readOnly": True,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot update a design layer request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_missing_required_fields(self):
        """Test that required fields are validated."""
        # Remove required fields
        variables_without_title = self.variables.copy()
        del variables_without_title["title"]

        response = self.client.execute(
            self.mutation, variables=variables_without_title, context=self.auth_request
        )
        # Check if the query fails with a required field error
        self.assertIn("errors", response)
        self.assertIn("title", response["errors"][0]["message"])

    def test_invalid_workspace_request_id(self):
        """Test that an invalid workspace request ID is handled properly."""
        # Use a non-existent workspace request ID
        invalid_variables = self.variables.copy()
        invalid_variables["workspaceRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a workspace request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceRequestId": "Dataset with 99999 not found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_workspace_request_regular_user(self):
        """Test that a regular user cannot update another user's workspace request."""
        # Create a workspace request owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        # Try to update the other user's workspace request
        other_variables = self.variables.copy()
        other_variables["workspaceRequestId"] = other_workspace_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {other_workspace_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_workspace_request_superuser(self):
        """Test that a superuser can update another user's workspace request."""
        # Create a workspace request owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        # Try to update the other user's workspace request as a superuser
        other_variables = self.variables.copy()
        other_variables["workspaceRequestId"] = other_workspace_request.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)

        # Verify the workspace request was updated
        data = response["data"]["updateDesignLayerRequest"]["workspaceRequest"]
        self.assertEqual(int(data["id"]), other_workspace_request.id)

        # Verify the layer data was updated
        layer_data = data["layerData"]
        self.assertEqual(layer_data["title"], other_variables["title"])
        self.assertEqual(layer_data["description"], other_variables["description"])
        self.assertEqual(layer_data["color"], other_variables["color"])
        self.assertEqual(layer_data["read_only"], other_variables["readOnly"])

        # Verify the database record
        other_workspace_request.refresh_from_db()
        self.assertEqual(
            other_workspace_request.layer_data["title"], other_variables["title"]
        )
        self.assertEqual(
            other_workspace_request.layer_data["description"],
            other_variables["description"],
        )
        self.assertEqual(
            other_workspace_request.layer_data["color"], other_variables["color"]
        )
        self.assertEqual(
            other_workspace_request.layer_data["read_only"], other_variables["readOnly"]
        )

    def test_successful_update(self):
        """Test successful update of a design layer request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["updateDesignLayerRequest"]["workspaceRequest"]
        self.assertEqual(int(data["id"]), self.workspace_request.id)

        # Verify the layer data was updated
        layer_data = data["layerData"]
        self.assertEqual(layer_data["title"], self.variables["title"])
        self.assertEqual(layer_data["description"], self.variables["description"])
        self.assertEqual(layer_data["color"], self.variables["color"])
        self.assertEqual(layer_data["read_only"], self.variables["readOnly"])

        # Verify the database record
        self.workspace_request.refresh_from_db()
        self.assertEqual(
            self.workspace_request.layer_data["title"], self.variables["title"]
        )
        self.assertEqual(
            self.workspace_request.layer_data["description"],
            self.variables["description"],
        )
        self.assertEqual(
            self.workspace_request.layer_data["color"], self.variables["color"]
        )
        self.assertEqual(
            self.workspace_request.layer_data["read_only"], self.variables["readOnly"]
        )
