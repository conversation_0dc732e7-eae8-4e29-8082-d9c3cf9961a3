"""
<?xml version="1.0" encoding="UTF-8"?>
<sld:StyledLayerDescriptor xmlns:sld="http://www.opengis.net/sld" xmlns="http://www.opengis.net/sld" xmlns:gml="http://www.opengis.net/gml" xmlns:ogc="http://www.opengis.net/ogc" version="1.0.0">
  <sld:NamedLayer>
    <sld:Name>39 - 11fdcfc4-f56d-4446-b512-892b95005d71</sld:Name>
    <sld:UserStyle>
      <sld:Name>39 - 11fdcfc4-f56d-4446-b512-892b95005d71</sld:Name>
      <sld:Title>39 - 11fdcfc4-f56d-4446-b512-892b95005d71_generic</sld:Title>
      <sld:Abstract>Style to display buffer geometry as a semi-transparent polygon and original geometries.</sld:Abstract>
      <sld:FeatureTypeStyle>
        <sld:Name>name</sld:Name>

        <!-- Style for Buffer Geometry -->
        <sld:Rule>
          <sld:Name>Buffer</sld:Name>
          <sld:Title>Buffer Geometry</sld:Title>
          <sld:PolygonSymbolizer>
            <sld:Geometry>
              <ogc:PropertyName>buffer_geometry</ogc:PropertyName>
            </sld:Geometry>
            <sld:Fill>
              <sld:CssParameter name="fill">#FFD700</sld:CssParameter>
              <sld:CssParameter name="fill-opacity">0.3</sld:CssParameter>
            </sld:Fill>
            <sld:Stroke>
              <sld:CssParameter name="stroke">#FFA500</sld:CssParameter>
              <sld:CssParameter name="stroke-width">1</sld:CssParameter>
            </sld:Stroke>
          </sld:PolygonSymbolizer>
        </sld:Rule>

        <!-- Rule for Original Polygon Geometry -->
        <sld:Rule>
          <sld:Name>Original Polygon</sld:Name>
          <sld:Title>Original Grey Polygon</sld:Title>
          <ogc:Filter>
            <ogc:PropertyIsEqualTo>
              <ogc:Function name="geometryType">
                <ogc:PropertyName>geometry</ogc:PropertyName>
              </ogc:Function>
              <ogc:Literal>Polygon</ogc:Literal>
            </ogc:PropertyIsEqualTo>
          </ogc:Filter>
          <sld:PolygonSymbolizer>
            <sld:Fill>
              <sld:CssParameter name="fill">#7679a2</sld:CssParameter> <!-- Grey color -->
              <sld:CssParameter name="fill-opacity">0.2</sld:CssParameter>
            </sld:Fill>
            <sld:Stroke>
              <sld:CssParameter name="stroke">#7679a2</sld:CssParameter>
            </sld:Stroke>
          </sld:PolygonSymbolizer>
        </sld:Rule>

        <!-- Rule for Line Geometry -->
        <sld:Rule>
          <sld:Name>Line</sld:Name>
          <sld:Title>Blue Line</sld:Title>
          <ogc:Filter>
            <ogc:PropertyIsEqualTo>
              <ogc:Function name="geometryType">
                <ogc:PropertyName>geometry</ogc:PropertyName>
              </ogc:Function>
              <ogc:Literal>LineString</ogc:Literal>
            </ogc:PropertyIsEqualTo>
          </ogc:Filter>
          <sld:LineSymbolizer>
            <sld:Stroke>
              <sld:CssParameter name="stroke">#7679a2</sld:CssParameter> <!-- Grey line color -->
            </sld:Stroke>
          </sld:LineSymbolizer>
        </sld:Rule>

        <!-- Rule for Point Geometry -->
        <sld:Rule>
          <sld:Name>Point</sld:Name>
          <sld:Title>Red Circle Point</sld:Title>
          <ogc:Filter>
            <ogc:PropertyIsEqualTo>
              <ogc:Function name="geometryType">
                <ogc:PropertyName>geometry</ogc:PropertyName>
              </ogc:Function>
              <ogc:Literal>Point</ogc:Literal>
            </ogc:PropertyIsEqualTo>
          </ogc:Filter>
          <sld:PointSymbolizer>
            <sld:Graphic>
              <sld:Mark>
                <sld:WellKnownName>circle</sld:WellKnownName>
                <sld:Fill>
                  <sld:CssParameter name="fill">#FF0000</sld:CssParameter> <!-- Red color -->
                  <sld:CssParameter name="fill-opacity">0.4</sld:CssParameter>
                </sld:Fill>
                <sld:Stroke>
                  <sld:CssParameter name="stroke">#FF0000</sld:CssParameter>
                </sld:Stroke>
              </sld:Mark>
              <sld:Size>6</sld:Size>
            </sld:Graphic>
          </sld:PointSymbolizer>
        </sld:Rule>

      </sld:FeatureTypeStyle>
    </sld:UserStyle>
  </sld:NamedLayer>
</sld:StyledLayerDescriptor>
"""
