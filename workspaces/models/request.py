from django.db import models
from django.db.models.query_utils import Q
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from organizations.models import Organization
from users.models import User


class WorkspaceRequestManager(models.Manager):
    def permitted_objects(self, user: User, organization: Organization, **kwargs):
        filters = dict(organization=organization)
        if not user.is_superuser:
            filters["created_by"] = user
        return super().get_queryset().filter(Q(**kwargs), Q(**filters))


class WorkspaceRequestChoices(models.TextChoices):
    IN_PROGRESS = "in_progress", _("In Progress")
    FINISHED = "finished", _("Finished")
    CANCELLED = "cancelled", _("Cancelled")


class RequestTypeChoices(models.TextChoices):
    UPLOAD_FILE = "upload_file", _("Upload File")
    DESIGN_LAYER = "design_layer", _("Design Layer")
    CONNECT_DB = "connect_db", _("Connect Database")


class WorkspaceRequest(TimeStampedModel):
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="requests",
        verbose_name=_("Created by"),
    )
    organization = models.ForeignKey(
        "organizations.Organization",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="requests",
        verbose_name=_("Organization"),
    )
    dataset = models.ForeignKey(
        "workspaces.Dataset",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="requests",
        verbose_name=_("Dataset"),
    )
    layer = models.ForeignKey(
        "layers.Layer",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="requests",
        verbose_name=_("Layer"),
    )
    status = models.CharField(
        choices=WorkspaceRequestChoices.choices,
        default=WorkspaceRequestChoices.IN_PROGRESS,
        max_length=20,
        db_index=True,
        verbose_name=_("Request Status"),
    )
    request_type = models.CharField(
        choices=RequestTypeChoices.choices,
        default=RequestTypeChoices.UPLOAD_FILE,
        max_length=20,
        db_index=True,
        verbose_name=_("Request Type"),
    )
    layer_data = JSONField(blank=True, default=dict, verbose_name=_("Layer Data"))
    objects = WorkspaceRequestManager()

    class Meta:
        verbose_name = _("Dataset Request")
        verbose_name_plural = _("Dataset Requests")

    def __str__(self):
        return f"pk: {self.pk} - status: {self.status}"

    def set_layer_data(self, layer_data):
        self.layer_data = {**self.layer_data, **layer_data}
        self.save(update_fields=["layer_data", "modified"])

    def finish(self, layer):
        self.status = WorkspaceRequestChoices.FINISHED
        self.layer = layer
        self.save(update_fields=["status", "layer", "modified"])

    @property
    def current_step(self) -> int:
        if self.status != WorkspaceRequestChoices.IN_PROGRESS:
            return 0
        if self.request_type == RequestTypeChoices.UPLOAD_FILE:
            return self.get_upload_file_current_step()
        if self.request_type == RequestTypeChoices.DESIGN_LAYER:
            return self.get_design_layer_current_step()
        return 0

    def get_upload_file_current_step(self) -> int:
        layer_data = self.layer_data or dict()
        if layer_data.get("json_schema"):
            return 5
        if self.dataset.meta_data.get("eda_reports"):
            return 3
        if layer_data.get("location_field_mapping"):
            return 4
        if layer_data.get("sample_data"):
            return 2
        return 1

    def get_design_layer_current_step(self) -> int:
        layer_data = self.layer_data or dict()
        if layer_data.get("json_schema"):
            return 2
        return 1
