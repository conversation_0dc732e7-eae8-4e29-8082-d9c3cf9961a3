import os
from unittest.mock import patch, MagicMock

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.handlers.dataset_files import DatasetFilesLoader
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequestChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestDatasetSampleData(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.csv"),
        )
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            layer_data={
                "color": "#42FAC2",
                "title": "test dataset",
                "created": "2024-12-06T19:07:40",
                "read_only": False,
                "description": "ny desc",
                "json_schema": {},
                "web_ui_json_schema": None,
                "location_field_mapping": {
                    "coordinate_type": "point",
                    "lang_lat_column": "",
                    "latitude_column": "X",
                    "longitude_column": "Y",
                    "lat_lon_column_num": "two_column",
                },
            },
        )
        self.query = """
        query MyQuery($datasetRequestId: Int!, $orgId: Int!) {
          datasetSampleData(datasetRequestId: $datasetRequestId, orgId: $orgId) {
            data {
              data
              column
            }
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "orgId": self.organization.id,
        }

    def test_invalid_file_format(self):
        """Test handling of unsupported file formats."""
        # We'll use the existing dataset but mock the DatasetFilesLoader
        # to simulate an unsupported file format

        # Mock the DatasetFilesLoader.load_data method to raise a ValueError
        with patch.object(DatasetFilesLoader, "load_data") as mock_load_data:
            mock_load_data.side_effect = ValueError("Unsupported file format: xyz")

            # Execute the query
            response = self.client.execute(
                self.query, variables=self.variables, context=self.auth_request
            )

            # Check if the query returns an error
            self.assertIn("errors", response)
            self.assertIn("Unsupported file format", response["errors"][0]["message"])

    def test_missing_file(self):
        """Test handling of missing dataset files."""
        # We'll use the existing dataset but mock the StorageBackend
        # to simulate a missing file

        # Mock the StorageBackend.open method to raise a FileNotFoundError
        with patch("common.utils.StorageBackend.open") as mock_open:
            mock_open.side_effect = FileNotFoundError("File not found")

            # Execute the query
            response = self.client.execute(
                self.query, variables=self.variables, context=self.auth_request
            )

            # Check if the query returns an error
            self.assertIn("errors", response)
            self.assertIn("File not found", response["errors"][0]["message"])

    def test_empty_dataset(self):
        """Test handling of empty dataset files."""
        # We'll use the existing dataset but mock the DatasetFilesLoader
        # to simulate an empty dataset

        # Mock the DatasetFilesLoader.get_sample_data method to return an empty list
        with patch.object(
            DatasetFilesLoader, "get_sample_data"
        ) as mock_get_sample_data:
            mock_get_sample_data.return_value = []

            # Execute the query
            response = self.client.execute(
                self.query, variables=self.variables, context=self.auth_request
            )

            # Check if the query is successful but returns empty data
            self.assertNotIn("errors", response)
            data = response["data"]["datasetSampleData"]["data"]
            self.assertEqual(len(data), 0)

    def test_successful_data_retrieval(self):
        """Test successful retrieval of dataset sample data."""
        # Execute the query without any mocks
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )

        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Get the data from the response
        data = response["data"]["datasetSampleData"]["data"]

        # Verify that data is returned
        self.assertGreater(len(data), 0)

        # Create a DatasetFilesLoader instance and get sample data
        loader = DatasetFilesLoader(file=self.dataset.file)
        expected_data = loader.get_sample_data(
            rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER
        )

        # Verify that the returned data matches the expected data
        self.assertEqual(data, expected_data)
