import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequestChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestUpdateJSONSchemas(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg"),
            meta_data={},
        )
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
        )
        self.file_path = os.path.join(settings.MEDIA_ROOT, "sample_gpkg.gpkg")
        self.mutation = """
            mutation MyMutation($datasetRequestId: Int!, $formData: JSONString!, $jsonSchema: JSONString!, $webUiJsonSchema: JSONString!, $orgId: Int!) {
              updateJsonSchemas(
                dataInput: {datasetRequestId: $datasetRequestId, formData: $formData, jsonSchema: $jsonSchema, webUiJsonSchema: $webUiJsonSchema, orgId: $orgId}
              ) {
                datasetRequest {
                  currentStep
                  layerData
                  status
                  dataset {
                    metaData
                  }
                  createdBy {
                    email
                  }
                }
              }
            }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "formData": '{"test":"test"}',
            "jsonSchema": "{}",
            "webUiJsonSchema": "{}",
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_validate_and_get_data_func(self):
        self.dataset_request.status = WorkspaceRequestChoices.CANCELLED
        self.dataset_request.save()
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "workspaceRequestId": _("Dataset with %(request_id)s not found")
                % {"request_id": self.variables["datasetRequestId"]}
            },
        )

    def test_check_permissions_func(self):
        user = UserFactory(is_superuser=False)
        dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            layer_data={"data": {"data": "data"}},
            organization=self.organization,
        )
        self.variables["datasetRequestId"] = dataset_request.id
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"workspaceRequestId": f"Dataset with {dataset_request.id} not found"},
        )

    def test_handle_func(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["updateJsonSchemas"]
        self.assertDictEqual(
            data["datasetRequest"]["layerData"],
            {"json_schema": {}, "web_ui_json_schema": {}},
        )
