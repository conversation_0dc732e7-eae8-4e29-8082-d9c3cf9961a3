from enum import Enum

import graphene


class QuestionTypeEnum(Enum):
    DATA = "data"
    GIS = "gis"
    DATA_ACTION = "data_action"
    GENERAL = "general"


class NewChatInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_id = graphene.Int(required=True)
    question = graphene.String(required=True)
    layers_ids = graphene.List(graphene.Int)
    question_type = graphene.Field(
        graphene.Enum.from_enum(QuestionTypeEnum), required=True
    )


class ResetNewChatInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_id = graphene.Int(required=True)
    layers_ids = graphene.List(graphene.Int)


EXECUTION_MAPPER = {
    QuestionTypeEnum.DATA.value: lambda instance: instance.execute_data_question,
    QuestionTypeEnum.GIS.value: lambda instance: instance.execute_gis_question,
    QuestionTypeEnum.DATA_ACTION.value: lambda instance: instance.execute_data_action_order,
    QuestionTypeEnum.GENERAL.value: lambda instance: instance.execute_general_question,
}
