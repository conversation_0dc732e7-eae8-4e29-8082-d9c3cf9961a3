from django.apps import AppConfig
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class CommonConfig(AppConfig):
    name = "common"
    verbose_name = _("Common")

    def ready(self):
        # Configure the elasticsearch connections
        from elasticsearch_dsl.connections import connections

        connection_settings = settings.ES_CONNECTION
        connections.configure(**connection_settings)
