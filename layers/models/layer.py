from django.contrib.gis.db import models
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import J<PERSON><PERSON>ield

from layers.models.typed_dict import get_default_sld_data


class SLDMapTypes(models.TextChoices):
    POINTS = "points", _("Points")
    HEATMAP = "heatmap", _("Heatmap")


class LayerStatusChoices(models.TextChoices):
    PUBLISHED = "published", _("Published")
    UNPUBLISHED = "unpublished", _("Unpublished")


class Layer(TimeStampedModel):
    dataset = models.ForeignKey(
        "workspaces.Dataset",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="layers",
        verbose_name=_("Dataset"),
    )
    workspace = models.ForeignKey(
        "workspaces.Workspace",
        on_delete=models.CASCADE,
        related_name="layers",
        verbose_name=_("Workspace"),
    )
    slds = models.ManyToManyField(
        "layers.SLD", related_name="layers", blank=True, verbose_name=_("SLDs")
    )
    key = models.SlugField(
        max_length=200, unique=True, verbose_name=_("Unique Form Key")
    )
    title = models.CharField(max_length=200, verbose_name=_("Layer Title"))
    description = models.TextField(
        null=True, blank=True, verbose_name=_("Layer Description")
    )
    read_only = models.BooleanField(
        default=False,
        verbose_name=_("Read Only"),
        help_text=_("Is this layer editable or not?"),
    )
    status = models.CharField(
        max_length=20,
        choices=LayerStatusChoices.choices,
        default=LayerStatusChoices.UNPUBLISHED,
        db_index=True,
        verbose_name=_("Layer Status"),
    )
    boundaries = models.GeometryField(
        null=True, blank=True, verbose_name=_("Boundaries")
    )
    location_field_mapping = JSONField(
        verbose_name=_("Location Field Mapping"),
        blank=True,
        null=True,
        default=dict,
    )
    json_schema = JSONField(
        blank=True,
        null=True,
        verbose_name=_("JSON Schema"),
        help_text=_("Form and UI Schemas"),
    )
    web_ui_json_schema = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Layer Web UI JsonSchema"),
    )
    data = JSONField(
        blank=True,
        default=dict,
        verbose_name=_("Layer Data"),
        help_text=_("Layer extra data"),
    )
    records_last_modified = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Records Last Modified")
    )
    filters = JSONField(
        blank=True,
        default=dict,
        verbose_name=_("Layer Filters"),
        help_text=_("Layer filters"),
    )

    class Meta:
        verbose_name = _("Layer")
        verbose_name_plural = _("Layers")

    def __str__(self):
        return self.key

    @cached_property
    def records_count(self):
        return self.records.count()

    def publish(self):
        self.status = LayerStatusChoices.PUBLISHED
        self.save(update_fields=["status", "modified"])

    def unpublish(self):
        self.status = LayerStatusChoices.UNPUBLISHED
        self.save(update_fields=["status", "modified"])

    def update_records_last_modified(self):
        self.records_last_modified = timezone.now()
        self.save(update_fields=["records_last_modified"])

    def is_eda_report_outdated(self, dataset):
        """
        checks if the timestamp of the most recently modified records (`records_last_modified`)
        is later than the creation time of the latest EDA report.
        """

        latest_report = dataset.eda_reports.order_by("-created").first()
        if not (latest_report and self.records_last_modified):
            return True
        return self.records_last_modified > latest_report.created


class SLD(TimeStampedModel):
    title = models.CharField(max_length=200, verbose_name=_("SLD Title"))
    sld_type = models.CharField(
        choices=SLDMapTypes.choices,
        max_length=20,
        default=SLDMapTypes.POINTS.value,
        verbose_name=_("SLD Type"),
    )
    feature_style = JSONField(
        blank=True, default=get_default_sld_data, verbose_name=_("SLD Data")
    )
    xml_body = models.TextField(blank=True, default="", verbose_name=_("XML Body"))

    class Meta:
        verbose_name = _("SLD")
        verbose_name_plural = _("SLDs")
        constraints = [
            models.UniqueConstraint(
                fields=["title", "sld_type"], name="unique_sld_title_type"
            )
        ]

    def __str__(self):
        return f"{self.title} - {self.sld_type}"
