from django.conf import settings

ORGANIZATION_MANAGERS = "Organization Managers"
ORGANIZATION_EDITORS = "Organization Editors"
ORGANIZATION_VIEWERS = "Organization Viewers"
ORGANIZATION_USERS = "Organization Users"
ORGANIZATION_OWNER = getattr(settings, "MAIN_OWNER_ROLE_CODENAME", "main-owner")

VIEW_USER = "view_user"
ADD_USER = "add_user"
CHANGE_USER = "change_user"
DELETE_USER = "delete_user"

VIEW_ORGANIZATION = "view_organization"
ADD_ORGANIZATION = "add_organization"
CHANGE_ORGANIZATION = "change_organization"
DELETE_ORGANIZATION = "delete_organization"

VIEW_LAYER = "view_layer"
ADD_LAYER = "add_layer"
DELETE_LAYER = "delete_layer"
EDIT_LAYER = "edit_layer"

VIEW_RECORD = "view_record"
ADD_RECORD = "add_record"
DELETE_RECORD = "delete_record"
EDIT_RECORD = "edit_record"

ADD_WORKSPACE = "add_workspace"
CHANGE_WORKSPACE = "change_workspace"
DELETE_WORKSPACE = "delete_workspace"
VIEW_WORKSPACE = "view_workspace"

ADD_ROLE = "add_role"
CHANGE_ROLE = "change_role"
DELETE_ROLE = "delete_role"
VIEW_ROLE = "view_role"

permissions_matrix = {
    ORGANIZATION_OWNER: [
        # organization permissions
        VIEW_ORGANIZATION,
        ADD_ORGANIZATION,
        CHANGE_ORGANIZATION,
        DELETE_ORGANIZATION,
        # user permissions
        VIEW_USER,
        ADD_USER,
        CHANGE_USER,
        DELETE_USER,
        # Layer Management
        VIEW_LAYER,
        ADD_LAYER,
        EDIT_LAYER,
        DELETE_LAYER,
        # Layer record management like Point(Pin), Polygon, LineString
        VIEW_RECORD,
        ADD_RECORD,
        DELETE_RECORD,
        EDIT_RECORD,
        # Workspace Management
        ADD_WORKSPACE,
        CHANGE_WORKSPACE,
        DELETE_WORKSPACE,
        VIEW_WORKSPACE,
        # Role Management
        ADD_ROLE,
        CHANGE_ROLE,
        DELETE_ROLE,
        VIEW_ROLE,
    ],
    ORGANIZATION_MANAGERS: [
        # organization permissions
        VIEW_ORGANIZATION,
        # user permissions
        VIEW_USER,
        ADD_USER,
        CHANGE_USER,
        DELETE_USER,
        # Layer Management
        VIEW_LAYER,
        ADD_LAYER,
        EDIT_LAYER,
        DELETE_LAYER,
        # Layer record management like Point(Pin), Polygon, LineString
        VIEW_RECORD,
        ADD_RECORD,
        DELETE_RECORD,
        EDIT_RECORD,
        # Workspace Management
        ADD_WORKSPACE,
        CHANGE_WORKSPACE,
        DELETE_WORKSPACE,
        VIEW_WORKSPACE,
        # Role Management
        ADD_ROLE,
        CHANGE_ROLE,
        VIEW_ROLE,
    ],
    ORGANIZATION_EDITORS: [
        # organization permissions
        VIEW_ORGANIZATION,
        # Layer Management
        VIEW_LAYER,
        # Layer record management like Point(Pin), Polygon, LineString
        VIEW_RECORD,
        ADD_RECORD,
        DELETE_RECORD,
        EDIT_RECORD,
        # Workspace Management
        ADD_WORKSPACE,
        CHANGE_WORKSPACE,
        VIEW_WORKSPACE,
    ],
    ORGANIZATION_VIEWERS: [
        VIEW_ORGANIZATION,
        VIEW_LAYER,
        VIEW_RECORD,
        VIEW_USER,
        VIEW_WORKSPACE,
        VIEW_ROLE,
    ],
    ORGANIZATION_USERS: [VIEW_ORGANIZATION],
}
