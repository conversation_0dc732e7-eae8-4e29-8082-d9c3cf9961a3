import logging

from django.core.management.base import BaseCommand

from common.utils.eda_generator import EDAReportGenerator
from layers.models import Layer

logger = logging.getLogger("workspaces")


class Command(BaseCommand):
    help = "Creates EDA reports for layers"

    def add_arguments(self, parser):
        parser.add_argument(
            "--layers_ids",
            type=lambda s: [int(item.strip()) for item in s.split(",")],
            required=False,
            help="Create EDA for specific layers",
        )

    def handle(self, *args, **kwargs):
        layers_ids = kwargs.get("layers_ids")

        # Get layers based on provided IDs or all layers if no IDs provided
        layers = Layer.objects.select_related("dataset")
        if layers_ids:
            layers = layers.filter(id__in=layers_ids)
            logger.debug(f"Processing {len(layers)} specified layers")
        else:
            logger.debug(f"Processing all layers ({layers.count()})")

        # Filter layers to only those with outdated EDA reports
        outdated_layers = [
            layer for layer in layers if layer.is_eda_report_outdated(layer.dataset)
        ]
        logger.debug(f"Found {len(outdated_layers)} layers with outdated EDA reports")

        if not outdated_layers:
            logger.debug(self.style.SUCCESS("No layers need EDA report updates"))
            return

        # Create EDA reports for outdated layers
        eda_generator = EDAReportGenerator(identifier="layer_eda_reports")

        success_layers = []
        error_layers = []

        for layer in outdated_layers:
            logger.debug(
                f"Generating EDA report for layer: {layer.title} (ID: {layer.id})"
            )
            try:
                eda_generator.identifier += f"_{layer.id}"
                eda_report_data = eda_generator.generate_eda_reports_from_layer(
                    layer=layer, title=f"Layer {layer.title}"
                )
            except Exception as e:
                logger.debug(
                    f"Error generating EDA report for layer {layer.title}: {str(e)}"
                )
                error_layers.append(layer.id)
                continue

            # Create the EDA report record
            layer.dataset.create_eda_reports(eda_report_data)
            success_layers.append(layer.id)
            logger.debug(f"Successfully created EDA report for layer: {layer.title}")

        logger.debug("EDA report generation completed")
        logger.debug(f"Success layers Count: {len(success_layers)}")
        logger.debug(f"Error layers Count: {len(error_layers)}")
        logger.debug(f"Success layers IDs: {success_layers}")
        logger.debug(f"Error layers IDs: {error_layers}")
