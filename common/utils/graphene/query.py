import datetime
import json
import typing
from enum import Enum

import graphene
from django.core.exceptions import FieldError
from django.db.models import Q, F
from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import NotFound


def evaluate_boolean_string(s):
    if s.lower() == "false" or s == "0":
        return False
    return bool(s)


_conversion_methods = {
    # string conversions
    "exact": lambda x: x,
    "iexact": lambda x: x,
    "contains": lambda x: x,
    "icontains": lambda x: x,
    # numbers conversions
    "gt": float,
    "lt": float,
    "gte": float,
    "lte": float,
    "range": lambda x: json.loads(x.replace("'", '"')),
    # date time conversions
    "date__gt": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__lt": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__gte": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__lte": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "time__gt": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__lt": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__gte": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__lte": lambda x: datetime.time.fromisoformat(x).isoformat(),
    # ids_ in clause conversions
    "in": lambda x: json.loads(x.replace("'", '"')),
    # is null
    "isnull": evaluate_boolean_string,
    "isempty": lambda x: None,
}


class BoundedInt(graphene.Int):
    """A custom Graphene Int that has bounded values between a min and max."""

    @staticmethod
    def parse_literal(node):
        # Convert the AST node to a Python int
        try:
            value = int(node.value)
        except ValueError:
            raise ValueError(_("Value provided is not an integer"))
        # Here you would include your custom validation logic, for example:
        return BoundedInt.validate(value)

    @staticmethod
    def parse_value(value):
        return BoundedInt.validate(value)

    @staticmethod
    def validate(value, min_value=0):
        """Validate the integer is within the min and max bounds."""
        if value is None:
            return None

        if min_value > value:
            raise ValueError(
                _("Value must be bigger than %(min_value)s" % {"min_value": min_value})
            )
        return value


class PageInfo(graphene.InputObjectType):
    limit = BoundedInt()
    offset = BoundedInt()
    order_by = graphene.String()


class DjangoFilterClauses(Enum):
    # text clauses
    exact = "exact"
    iexact = "iexact"
    contains = "contains"
    icontains = "icontains"

    # values in clauses
    values_in = "in"

    # numbers
    gt = "gt"
    lt = "lt"
    gte = "gte"
    lte = "lte"
    range = "range"
    # dates
    date__gt = "date__gt"
    date__lt = "date__lt"
    date__gte = "date__gte"
    date__lte = "date__lte"

    # times
    time__gt = "time__gt"
    time__lt = "time__lt"
    time__gte = "time__gte"
    time__lte = "time__lte"

    # is null
    isnull = "isnull"

    # is empty
    isempty = "isempty"


class FilterOperatorEnum(Enum):
    # Logical operators
    AND = "AND"
    OR = "OR"

    # Field operators (inherit from DjangoFilterClauses)
    exact = "exact"
    iexact = "iexact"
    contains = "contains"
    icontains = "icontains"
    values_in = "in"
    gt = "gt"
    lt = "lt"
    gte = "gte"
    lte = "lte"
    range = "range"
    date__gt = "date__gt"
    date__lt = "date__lt"
    date__gte = "date__gte"
    date__lte = "date__lte"
    time__gt = "time__gt"
    time__lt = "time__lt"
    time__gte = "time__gte"
    time__lte = "time__lte"
    isnull = "isnull"
    isempty = "isempty"


DjangoFilterChoices = graphene.Enum.from_enum(DjangoFilterClauses)
FilterOperatorChoices = graphene.Enum.from_enum(FilterOperatorEnum)


class DjangoFilterInput(graphene.InputObjectType):
    field = graphene.String(required=True)
    value = graphene.String(required=True)
    clause = graphene.Field(DjangoFilterChoices)
    is_not = graphene.Boolean()
    or_group = graphene.Int()


class NestedFilterInput(graphene.InputObjectType):
    """
    Nested filter input that supports hierarchical logical operations.

    For logical operators (AND, OR, MATCH):
    - op: The logical operator
    - content: List of nested filters
    - negate_op: Whether to negate the entire group

    For field operators (exact, gt, lt, etc.):
    - op: The field operator
    - field: Field name to filter on
    - value: List of values to compare against
    - negate_op: Whether to negate this specific filter
    """

    op = graphene.Field(FilterOperatorChoices, required=True)
    content = graphene.List(lambda: NestedFilterInput)  # Self-referencing
    field = graphene.String()
    value = graphene.List(graphene.String)
    negate_op = graphene.Boolean(default_value=False)


def filter_qs_paginate_with_count(qs, q: Q, page_info: typing.Dict = None):
    page_info = page_info or dict()
    limit = page_info.get("limit", 100_000)
    offset = page_info.get("offset", 0)
    order_by = "__".join(page_info.get("order_by", "").strip().split("."))
    if order_by:
        # validate if field exists in qs fields
        if order_by.startswith("-"):
            order_q = F(order_by[1:]).desc(nulls_last=True)
        else:
            order_q = F(order_by).asc(nulls_first=False)
        try:
            return (
                qs.filter(q).order_by(order_q)[offset : limit + offset],
                qs.filter(q).count(),
            )
        except FieldError:
            raise NotFound(reason={"order_by": f"invalid '{order_by}' field name"})
    print(f"SQL Query: {qs.filter(q).query}")
    return qs.filter(q)[offset : limit + offset], qs.filter(q).count()


def build_q(pk: int = None, filters: typing.List = None):
    # check if there is no filters list
    if filters is None:
        filters = list()

    # clone filters to avoid changing reference of filters in memory
    _filters = [*filters]

    # inject pk filter to return a list of one object
    if pk is not None:
        _filters.append({"field": "pk", "value": pk})

    # A list to hold AND conditions
    and_conditions = list()
    # A dictionary to hold OR groups
    or_groups = dict()

    for f in _filters:
        field = f.get("field")
        clause = f.get("clause", "exact")
        value = _conversion_methods.get(clause)(f.get("value"))
        is_not = f.get("is_not", False)
        # ex: (or_group = 1) any filter with or_group = 1 will be combined with OR
        or_group = f.get("or_group")

        q_dict = (
            {f"{field}": value}
            if clause == "isempty"
            else {f"{field}__{clause}": value}
        )
        q_obj = ~Q(**q_dict) if is_not else Q(**q_dict)
        if or_group:
            # setdefault to create a new list if or_group is not in the dictionary
            or_groups.setdefault(or_group, []).append(q_obj)
        else:
            # insert q_obj to the and_conditions list
            and_conditions.append(q_obj)

    # Combine OR groups
    for group in or_groups.values():
        combined = group[0]
        for q in group[1:]:
            combined |= q
        and_conditions.append(combined)

    # Combine all conditions with AND
    if and_conditions:
        combined_q = and_conditions[0]
        for q in and_conditions[1:]:
            combined_q &= q
        return combined_q

    return Q()


def build_nested_q(nested_filter: typing.Dict = None, pk: int = None):
    """
    Build the Django Q object from the nested filter structure.

    Args:
        nested_filter: Dictionary representing the nested filter structure
        pk: Optional primary key to add as an exact filter

    Returns:
        Django Q object
    """
    if nested_filter is None and pk is None:
        return Q()

    # Handle pk injection
    if pk is not None:
        pk_filter = {
            "op": "exact",
            "field": "pk",
            "value": [str(pk)],
            "negate_op": False,
        }
        if nested_filter is None:
            nested_filter = pk_filter
        else:
            # Wrap the existing filter with the AND add pk filter
            nested_filter = {
                "op": "AND",
                "content": [nested_filter, pk_filter],
                "negate_op": False,
            }

    return _build_q_recursive(nested_filter)


def _build_q_recursive(
    filter_dict: typing.Dict, max_depth: int = 50, current_depth: int = 0
):
    """
    Recursively build the Q object from the nested filter dictionary.

    Args:
        filter_dict: Filter dictionary with op, content, field, value, negate_op
        max_depth: Maximum recursion depth to prevent infinite loops
        current_depth: Current recursion depth

    Returns:
        Django Q object
    """
    if current_depth > max_depth:
        raise ValueError("Filter nesting too deep. Maximum depth is 50.")

    if not filter_dict or not isinstance(filter_dict, dict):
        return Q()

    op = filter_dict.get("op")
    negate_op = filter_dict.get("negate_op", False)

    if not op:
        return Q()

    # Handle logical operators
    if op in ["AND", "OR"]:
        content = filter_dict.get("content", [])
        if not content:
            return Q()

        # Process all nested filters
        q_objects = []
        combined_q = Q()
        for nested_filter in content:
            q_obj = _build_q_recursive(nested_filter, max_depth, current_depth + 1)
            if not q_obj:
                continue
            q_objects.append(q_obj)
            if op == "AND":
                combined_q &= q_obj
            else:
                combined_q |= q_obj

        return combined_q

    # Handle field operators
    else:
        field = filter_dict.get("field")
        values = filter_dict.get("value", [])

        if not field or not values:
            return Q()

        # Convert values based on the operator
        conversion_func = _conversion_methods.get(op, lambda x: x)

        converted_value = values
        if len(values) == 1:
            converted_value = conversion_func(values[0])

        # Build the Q object
        if op == "isempty":
            q_dict = {f"{field}": converted_value}
        else:
            q_dict = {f"{field}__{op}": converted_value}

        q_obj = Q(**q_dict)

        # Apply negation if needed
        return ~q_obj if negate_op else q_obj


__all__ = [
    "DjangoFilterInput",
    "NestedFilterInput",
    "PageInfo",
    "filter_qs_paginate_with_count",
    "build_q",
    "build_nested_q",
]
