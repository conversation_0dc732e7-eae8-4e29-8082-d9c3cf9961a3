# Generated by Django 3.2.25 on 2024-11-06 09:20

import django.db.models.deletion
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0003_auto_20241102_2224"),
        ("layers", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="layer",
            name="dataset",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="layer",
                to="workspaces.dataset",
                verbose_name="Dataset",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="layer",
            name="json_schema",
            field=jsoneditor.fields.django3_jsonfield.JSONField(
                blank=True,
                help_text="Form and UI Schemas",
                null=True,
                verbose_name="JSON Schema",
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="layer",
            name="web_ui_json_schema",
            field=jsoneditor.fields.django3_jsonfield.JSONField(
                blank=True, null=True, verbose_name="Layer Web UI JsonSchema"
            ),
        ),
    ]
