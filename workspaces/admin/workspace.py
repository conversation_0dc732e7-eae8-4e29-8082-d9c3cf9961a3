from django.contrib import admin
from gabbro.acl.admin import RuleStackedAdmin

from workspaces.models import Workspace


class WorkspaceAdmin(admin.ModelAdmin):
    list_display = ["id", "name", "organization", "owner", "last_visited", "created"]
    list_display_links = ["id", "name"]
    search_fields = ["name", "description", "owner__email", "owner__phone"]
    inlines = (RuleStackedAdmin,)

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(Workspace, WorkspaceAdmin)
