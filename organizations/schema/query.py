import graphene

from common.utils import (
    PageInfo,
    DjangoFilterInput,
    authentication_required,
    filter_qs_paginate_with_count,
    build_q,
)
from organizations.models import Organization
from organizations.schema.object_types import OrganizationListType


class Query(graphene.ObjectType):
    organizations = graphene.Field(
        OrganizationListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @staticmethod
    @authentication_required
    def resolve_organizations(
        root, info, pk=None, page_info=None, filters=None, **kwargs
    ):
        user = info.context.user
        queryset = Organization.acl_objects_for_user(user=user, perms=[])
        return OrganizationListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )
