# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo
*.pot

# local settings
local_settings.py

# Logs:
*.log*

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# gedit backup
*~

# sqlitedb
db.sqlite3

# media path
media/
/static/

.idea/

app.yaml
queue.yaml
libs/
.env/
.venv/
.env
.DS_Store
*.swp
celerybeat-schedule*
celerybeat.pid
.vscode/
django_hydra/
temp/
venv/

.ipynb_checkpoints
