from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Forbidden

from common.interfaces import PermissionsInterface
from common.utils import authorize_user
from organizations.models import Organization
from organizations.perms_constants import CHANGE_WORKSPACE


class RecordPermissions(PermissionsInterface):
    def check_permissions(self, user, organization: Organization, context: dict = None):
        authorize_user(model_obj=organization, user=user, permission=CHANGE_WORKSPACE)
        record = context["record"]
        permissions = (record.layer.workspace.owner == user, user.is_superuser)
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission denied") % {}})
