from common.interfaces import Strategy, PermissionsInterface, InputValidation
from organizations.models import Organization
from users.models import User


class Service:
    def __init__(
        self,
        strategy: Strategy,
        perms: PermissionsInterface,
        validator: InputValidation,
    ):
        self.strategy = strategy
        self.validator = validator
        self.perms = perms

    def handle(
        self, user: User, organization: Organization, data_input: dict, **kwargs
    ):
        context = self.validator.validate_and_get_data(
            data_input=data_input, user=user, organization=organization, **kwargs
        )
        self.perms.check_permissions(
            user=user, organization=organization, context=context
        )
        return self.strategy.handle(
            user=user,
            context=context,
            data_input=data_input,
            organization=organization,
            **kwargs,
        )
