from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from workspaces.models import EDAReport


class EdaReportAdmin(admin.ModelAdmin):
    list_display = ["id", "dataset", "source", "created", "report_link"]
    list_filter = ["source"]
    list_display_links = ["id", "dataset"]

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def report_link(self, eda_report: EDAReport):
        return format_html(
            f'<a href="{eda_report.file}" target="_blank">View Report</a>'
        )

    report_link.allow_tags = True
    report_link.short_description = _("Report Link")


admin.site.register(EDAReport, EdaReportAdmin)
