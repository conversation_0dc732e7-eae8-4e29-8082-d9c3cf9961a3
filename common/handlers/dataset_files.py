from io import BytesIO
from json import JSONDecodeError
from typing import Union, List

import geopandas as gpd
import pandas as pd

from common.handlers.kml import KMLHandler
from common.utils import (
    StorageBackend,
    parse_all_properties,
    parse_list_properties,
    safe_geojson_loader,
)


class DatasetFilesLoader:
    """
    A handler class to load and sample data from various file formats.
    """

    def __init__(
        self,
        file: Union[str, BytesIO],
        columns: List[str] = None,
        excluded_columns: List[str] = None,
    ):
        """
        :param file: File path or file-like object containing the dataset.
        :param columns: List of columns to sample, If None, all columns are considered.
        :param excluded_columns: List of columns to exclude from the dataset.
        """
        self.file = file
        self.columns = columns
        self.excluded_columns = excluded_columns
        self.sample_df = None

    def get_sample_data(self, rows_number: int) -> List[dict[str, str | list]]:
        """
        Retrieve a sample of specific rows and columns from a file.
        :param rows_number: Number of rows to sample, If None, all rows are considered.
        :return: List of dictionaries, each containing a column name and a list of sampled values.
        """
        # Retrieve a DataFrame sample
        self.sample_df = self.load_data(rows_number=rows_number)
        # Transform DataFrame into a list of dictionaries (column_name, [values])
        sample_data = [
            {"column": col, "data": parse_list_properties(self.sample_df[col].tolist())}
            for col in self.sample_df.columns
        ]
        return sample_data

    def get_all_features(self):
        """
        Retrieve all data, optionally limiting the number of rows.
        :return: List of dictionaries, each containing a column name and a list of sampled values.
        """
        # Retrieve a DataFrame of all data
        df = self.load_data()
        data = [parse_all_properties(record) for record in df.to_dict(orient="records")]
        return data

    def load_data(self, rows_number: int = None) -> pd.DataFrame:
        """
        Load a sample of data from a file with specified columns and rows, excluding columns as necessary.
        :param rows_number: Number of rows to sample, If None, all rows are considered.
        :return: DataFrame containing the sample.
        """
        with StorageBackend().open(self.file) as file_obj:
            file_extension = self._get_file_extension(file_obj)

            if file_extension == "csv":
                df = self._load_csv(file_obj, rows_number)
            elif file_extension == "kml":
                df = self._load_kml(file_obj, rows_number)
            elif file_extension in ["geojson", "gpkg", "shp"]:
                df = self._load_geospatial(file_obj, rows_number)
            else:
                raise ValueError(f"Unsupported file format: {file_extension}")

            # Exclude columns if specified
            if self.excluded_columns:
                df = df.drop(columns=self.excluded_columns, errors="ignore")
        try:
            df = df.fillna("")  # Replace NaN or None with empty strings
        except Exception:
            pass
        return df

    def _load_csv(self, file_obj: BytesIO, rows_number: int = None) -> pd.DataFrame:
        if rows_number:
            return pd.read_csv(file_obj, usecols=self.columns, nrows=rows_number)
        return pd.read_csv(file_obj, usecols=self.columns)

    def _load_kml(self, file_obj: BytesIO, rows_number: int = None) -> pd.DataFrame:
        kml_handler = KMLHandler(file_obj)
        df = kml_handler.load_kml()
        if self.columns:
            df = df[self.columns]
        if rows_number:
            return df.head(rows_number)
        return df

    def _load_geospatial(
        self, file_obj: BytesIO, rows_number: int = None
    ) -> pd.DataFrame:
        try:
            df = gpd.read_file(file_obj, rows=rows_number)
        except JSONDecodeError:
            fixed_data = safe_geojson_loader(StorageBackend().open(file_obj.name))
            df = gpd.GeoDataFrame.from_features(fixed_data["features"])
            df.head(rows_number)
        if self.columns:
            df = df[self.columns]
        return df

    @staticmethod
    def _get_file_extension(file_obj) -> str:
        file_name = getattr(file_obj, "name", None) or "data." + "csv"
        return file_name.split(".")[-1].lower()
