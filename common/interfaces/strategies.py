import logging
from abc import ABC, abstractmethod

from organizations.models import Organization
from users.models import User


class Strategy(ABC):
    def __init__(self):
        self._logger = logging.getLogger(__name__)
        self.debug = lambda method, message: self._logger.debug(
            f"[{self.__class__}][{method}] {message}"
        )

    @abstractmethod
    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        raise NotImplementedError()
